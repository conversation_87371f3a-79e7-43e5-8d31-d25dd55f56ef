import { CliOptions } from '../types';
export declare class ScriptGenerator {
    private configLoader;
    private analyzer;
    private codebaseAnalyzer;
    private dbScriptGenerator;
    private redisScriptGenerator;
    constructor();
    generateScripts(options: CliOptions): Promise<void>;
    /**
     * 스키마 자동 추출 시도
     */
    private extractSchemaIfPossible;
    /**
     * 분석 결과를 파일로 저장
     */
    private saveAnalysisResult;
    private generateGlobalScripts;
    private generateWorldScripts;
    /**
     * Shell 스크립트 생성
     */
    private generateShellScripts;
    private cleanScriptsDirectory;
    /**
     * 전체 업데이트 마스터 스크립트 생성
     */
    private generateMasterUpdateScript;
    /**
     * 월드별 업데이트 스크립트 생성
     */
    private generateWorldUpdateScripts;
    /**
     * 단일 월드 업데이트 스크립트 생성
     */
    private generateSingleWorldUpdateScript;
    /**
     * Redis 전용 업데이트 스크립트 생성
     */
    private generateRedisUpdateScript;
    /**
     * Shell 스크립트 헤더 생성
     */
    private generateShellHeader;
}
//# sourceMappingURL=scriptGenerator.d.ts.map