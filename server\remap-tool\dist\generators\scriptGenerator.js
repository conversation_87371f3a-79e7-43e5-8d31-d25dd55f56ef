"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScriptGenerator = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const chalk_1 = __importDefault(require("chalk"));
const configLoader_1 = require("../config/configLoader");
const remapDataSummaryAnalyzer_1 = require("../analyzer/remapDataSummaryAnalyzer");
const databaseScriptGenerator_1 = require("./databaseScriptGenerator");
const redisScriptGenerator_1 = require("./redisScriptGenerator");
const codebaseAnalyzer_1 = require("../analyzer/codebaseAnalyzer");
class ScriptGenerator {
    constructor() {
        this.configLoader = new configLoader_1.ConfigLoader();
        this.analyzer = new remapDataSummaryAnalyzer_1.RemapDataSummaryAnalyzer();
        this.codebaseAnalyzer = new codebaseAnalyzer_1.CodebaseAnalyzer();
        this.dbScriptGenerator = new databaseScriptGenerator_1.DatabaseScriptGenerator();
        this.redisScriptGenerator = new redisScriptGenerator_1.RedisScriptGenerator();
    }
    async generateScripts(options) {
        console.log('📄 CSV 파일 읽는 중:', options.csvFile);
        // scripts 폴더 비우기
        console.log('🗑️ 기존 스크립트 폴더 정리 중...');
        await this.cleanScriptsDirectory(options.outputDir);
        // 1단계: 코드베이스 분석 수행 (항상 최신 상태로)
        console.log('🔍 코드베이스 분석 수행 중...');
        // 스키마 디렉토리 확인 및 자동 추출
        const schemaDir = options.schemaDir || './artifacts/schemas';
        const schemaExists = await fs_extra_1.default.pathExists(schemaDir);
        if (!schemaExists) {
            console.log(chalk_1.default.yellow('⚠️ 스키마 디렉토리가 없습니다.'));
            console.log(chalk_1.default.cyan('🔄 스키마 자동 추출을 시도합니다...'));
            try {
                await this.extractSchemaIfPossible(schemaDir);
                console.log(chalk_1.default.green('✅ 스키마 추출 완료'));
            }
            catch (error) {
                console.log(chalk_1.default.yellow('⚠️ 스키마 자동 추출 실패. 기본 분석을 수행합니다.'));
                console.log(chalk_1.default.yellow('💡 더 정확한 분석을 위해 "extract-schema" 명령을 수동으로 실행하세요.'));
            }
        }
        const finalSchemaDir = await fs_extra_1.default.pathExists(schemaDir) ? schemaDir : undefined;
        const codebaseAnalysisResult = await this.codebaseAnalyzer.analyzeCodebase(finalSchemaDir);
        console.log(`📊 분석 완료: ${codebaseAnalysisResult.databaseTables.length}개 테이블, ${codebaseAnalysisResult.redisKeys.length}개 Redis 패턴`);
        // 테이블이 없으면 경고 표시
        if (codebaseAnalysisResult.databaseTables.length === 0) {
            console.log(chalk_1.default.yellow('⚠️ 분석된 데이터베이스 테이블이 없습니다.'));
            console.log(chalk_1.default.yellow('💡 다음 중 하나를 시도해보세요:'));
            console.log(chalk_1.default.yellow('   1. extract-schema 명령으로 스키마를 먼저 추출'));
            console.log(chalk_1.default.yellow('   2. 데이터베이스 연결 설정 확인'));
            console.log(chalk_1.default.yellow('   3. 테이블 이름이 올바른 prefix(a_, w_, u_)를 가지는지 확인'));
        }
        // 분석 결과 저장
        await this.saveAnalysisResult(codebaseAnalysisResult, options.outputDir);
        // 2단계: CSV 파일 분석
        const analysisResult = await this.analyzer.analyzeCSV(options.csvFile);
        console.log(`📊 ${analysisResult.totalRecords}개 레코드 분석 완료`);
        // 설정 로드
        this.configLoader = new configLoader_1.ConfigLoader(options.configDir);
        const config = await this.configLoader.loadRemapToolConfig();
        // 월드별 매핑 생성
        const worldMappings = this.configLoader.generateWorldShardMapping(config);
        // 출력 디렉토리 생성
        await fs_extra_1.default.ensureDir(options.outputDir);
        // 1단계: 전역 스크립트 생성 (auth, world, userCache, order)
        await this.generateGlobalScripts(options, analysisResult.records, codebaseAnalysisResult);
        // 2단계: 월드별 스크립트 생성 (user 샤드)
        if (options.worldId) {
            // 특정 월드만 처리
            await this.generateWorldScripts(options, analysisResult.records, worldMappings, options.worldId, codebaseAnalysisResult);
        }
        else {
            // 모든 월드 처리
            const worldIds = [...new Set(analysisResult.records.map(r => r.gameServerId))];
            for (const worldId of worldIds) {
                await this.generateWorldScripts(options, analysisResult.records, worldMappings, worldId, codebaseAnalysisResult);
            }
        }
        console.log(chalk_1.default.green('✅ 모든 스크립트 생성 완료'));
        // 3단계: Shell 스크립트 생성
        await this.generateShellScripts(options, analysisResult.records, worldMappings, codebaseAnalysisResult);
    }
    /**
     * 스키마 자동 추출 시도
     */
    async extractSchemaIfPossible(schemaDir) {
        try {
            // 설정 로드
            const config = await this.configLoader.loadRemapToolConfig();
            // 스키마 추출기 생성 및 실행
            const { SchemaExtractor, saveSchemaToFile } = await Promise.resolve().then(() => __importStar(require('../extractor/schemaExtractor')));
            const extractor = new SchemaExtractor(config);
            // 추출할 데이터베이스 목록 정의
            const extractors = [
                { type: 'auth', name: 'Auth', dbName: config.sharedConfig.mysqlAuthDb.database },
                { type: 'world', name: 'World', dbName: config.worlds[0]?.mysqlWorldDb?.database },
                { type: 'user', name: 'User', dbName: config.worlds[0]?.mysqlUserDb?.sqlDefaultCfg?.database }
            ];
            // 각 데이터베이스별로 스키마 추출
            for (const { type, name, dbName } of extractors) {
                if (!dbName)
                    continue;
                try {
                    console.log(`📊 ${name} 스키마 추출 중...`);
                    await extractor.connect(type, type === 'user' ? '0' : undefined);
                    const schema = await extractor.extractSchema(type, dbName);
                    await saveSchemaToFile(schema, schemaDir);
                    await extractor.disconnect();
                    console.log(`✅ ${name} 스키마 추출 완료 (${schema.tables.length}개 테이블)`);
                }
                catch (error) {
                    console.warn(`⚠️ ${name} 스키마 추출 실패:`, error);
                }
            }
        }
        catch (error) {
            throw new Error(`스키마 추출 실패: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * 분석 결과를 파일로 저장
     */
    async saveAnalysisResult(analysisResult, outputDir) {
        try {
            // artifacts/reports 디렉토리 생성
            const reportsDir = path_1.default.join('./artifacts/reports');
            await fs_extra_1.default.ensureDir(reportsDir);
            // JSON 형식으로 저장 (기존 호환성 유지)
            const jsonPath = path_1.default.join(reportsDir, 'analysis-result.json');
            await fs_extra_1.default.writeFile(jsonPath, JSON.stringify(analysisResult, null, 2), 'utf8');
            console.log(`📊 분석 결과 저장: ${jsonPath}`);
            // 추가로 outputDir에도 저장 (JSON5 형식)
            const outputJsonPath = path_1.default.join(outputDir, 'analysis-result.json5');
            await fs_extra_1.default.writeFile(outputJsonPath, JSON.stringify(analysisResult, null, 2), 'utf8');
            console.log(`📊 분석 결과 저장: ${outputJsonPath}`);
        }
        catch (error) {
            console.warn(`⚠️ 분석 결과 저장 실패: ${error}`);
        }
    }
    async generateGlobalScripts(options, records, codebaseAnalysisResult) {
        console.log('🌐 전역 스크립트 생성 중 (auth, userCache, order)...');
        // 전역 SQL 스크립트 생성 (auth만 - world는 월드별)
        await this.dbScriptGenerator.generateGlobalScripts(records, options.outputDir, codebaseAnalysisResult);
        // 전역 Redis 스크립트 생성 (auth, userCache, order)
        await this.redisScriptGenerator.generateGlobalScripts(records, options.outputDir, codebaseAnalysisResult);
        console.log('✅ 전역 스크립트 생성 완료');
    }
    async generateWorldScripts(options, records, worldMappings, worldId, codebaseAnalysisResult) {
        console.log(`🌍 ${worldId} 월드 스크립트 생성 중...`);
        // 해당 월드의 레코드만 필터링
        const worldRecords = records.filter(r => r.gameServerId === worldId);
        if (worldRecords.length === 0) {
            console.log(chalk_1.default.yellow(`⚠️ ${worldId} 월드에 대한 레코드가 없습니다.`));
            return;
        }
        // 월드별 출력 디렉토리 생성
        const worldOutputDir = path_1.default.join(options.outputDir, worldId);
        await fs_extra_1.default.ensureDir(worldOutputDir);
        // 월드 매핑 찾기 (설정이 없으면 기본값 사용)
        let worldMapping = this.configLoader.getShardForWorld(worldId, worldMappings);
        if (!worldMapping) {
            console.log(chalk_1.default.yellow(`⚠️ ${worldId} 월드에 대한 설정을 찾을 수 없습니다. 기본 설정을 사용합니다.`));
            worldMapping = {
                worldId: worldId,
                shardId: 0,
                userDatabase: `user_${worldId}`,
                worldDatabase: `world_${worldId}`
            };
        }
        // 월드별 SQL 스크립트 생성 (user 샤드 + world 데이터베이스)
        await this.dbScriptGenerator.generateUserShardScripts(worldRecords, worldMapping, worldOutputDir, codebaseAnalysisResult);
        await this.dbScriptGenerator.generateWorldDatabaseScript(worldRecords, worldMapping, worldOutputDir, codebaseAnalysisResult);
        console.log(`✅ ${worldId} 월드 스크립트 생성 완료 (${worldRecords.length}개 레코드)`);
    }
    /**
     * Shell 스크립트 생성
     */
    async generateShellScripts(options, records, worldMappings, codebaseAnalysisResult) {
        console.log('🐚 Shell 스크립트 생성 중...');
        try {
            // artifacts/scripts 폴더 생성
            const scriptsDir = path_1.default.join(options.outputDir, 'scripts');
            await fs_extra_1.default.ensureDir(scriptsDir);
            // 전체 업데이트 스크립트 생성
            await this.generateMasterUpdateScript(records, worldMappings, scriptsDir, codebaseAnalysisResult);
            // 월드별 업데이트 스크립트 생성
            await this.generateWorldUpdateScripts(records, worldMappings, scriptsDir, codebaseAnalysisResult);
            // Redis 전용 업데이트 스크립트 생성
            await this.generateRedisUpdateScript(records, scriptsDir, codebaseAnalysisResult);
            console.log('✅ Shell 스크립트 생성 완료');
        }
        catch (error) {
            console.error('❌ Shell 스크립트 생성 중 오류 발생:', error);
        }
    }
    async cleanScriptsDirectory(outputDir) {
        try {
            if (await fs_extra_1.default.pathExists(outputDir)) {
                await fs_extra_1.default.remove(outputDir);
                console.log(`✅ 기존 스크립트 폴더 삭제 완료: ${outputDir}`);
            }
            await fs_extra_1.default.ensureDir(outputDir);
            console.log(`📁 새로운 스크립트 폴더 생성: ${outputDir}`);
        }
        catch (error) {
            console.log(chalk_1.default.yellow(`⚠️ 스크립트 폴더 정리 중 오류: ${error}`));
        }
    }
    /**
     * 전체 업데이트 마스터 스크립트 생성
     */
    async generateMasterUpdateScript(records, worldMappings, scriptsDir, codebaseAnalysisResult) {
        const fileName = 'update_all.sh';
        const filePath = path_1.default.join(scriptsDir, fileName);
        let script = this.generateShellHeader('전체 데이터베이스 업데이트', records.length);
        // MySQL 설정
        script += `
# MySQL 연결 설정
MYSQL_HOST="\${MYSQL_HOST:-localhost}"
MYSQL_PORT="\${MYSQL_PORT:-3306}"
MYSQL_USER="\${MYSQL_USER:-root}"
MYSQL_PASSWORD="\${MYSQL_PASSWORD:-}"

# Redis 연결 설정
REDIS_HOST="\${REDIS_HOST:-localhost}"
REDIS_PORT="\${REDIS_PORT:-6379}"

# 스크립트 디렉토리
SCRIPT_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
SQL_DIR="\$(dirname "\$SCRIPT_DIR")"

echo "🚀 전체 데이터베이스 업데이트 시작..."
echo "📁 SQL 스크립트 디렉토리: \$SQL_DIR"

# 에러 발생시 중단
set -e

`;
        // Auth 데이터베이스 업데이트
        script += `
echo "🔐 Auth 데이터베이스 업데이트 중..."
if [ -f "\$SQL_DIR/auth_update.sql" ]; then
    mysql -h"\$MYSQL_HOST" -P"\$MYSQL_PORT" -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" auth < "\$SQL_DIR/auth_update.sql"
    echo "✅ Auth 데이터베이스 업데이트 완료"
else
    echo "⚠️ auth_update.sql 파일을 찾을 수 없습니다."
fi

`;
        // 월드별 데이터베이스 업데이트
        const worldIds = [...new Set(records.map(r => r.gameServerId))];
        for (const worldId of worldIds) {
            script += `
echo "🌍 ${worldId} 월드 데이터베이스 업데이트 중..."

# World 데이터베이스 업데이트
if [ -f "\$SQL_DIR/${worldId}/world_update.sql" ]; then
    mysql -h"\$MYSQL_HOST" -P"\$MYSQL_PORT" -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" world < "\$SQL_DIR/${worldId}/world_update.sql"
    echo "✅ ${worldId} World 데이터베이스 업데이트 완료"
else
    echo "⚠️ ${worldId}/world_update.sql 파일을 찾을 수 없습니다."
fi

# User Shard 데이터베이스 업데이트
for shard_file in "\$SQL_DIR/${worldId}"/user_shard_*_update.sql; do
    if [ -f "\$shard_file" ]; then
        shard_name=\$(basename "\$shard_file" .sql | sed 's/_update//')
        echo "👥 ${worldId} \$shard_name 데이터베이스 업데이트 중..."
        mysql -h"\$MYSQL_HOST" -P"\$MYSQL_PORT" -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" "\$shard_name" < "\$shard_file"
        echo "✅ ${worldId} \$shard_name 데이터베이스 업데이트 완료"
    fi
done

`;
        }
        // Redis 업데이트
        script += `
echo "🔴 Redis 데이터 업데이트 중..."

# 전역 Redis 스크립트 실행
for lua_file in "\$SQL_DIR"/*_redis_update.lua; do
    if [ -f "\$lua_file" ]; then
        echo "📄 실행 중: \$(basename "\$lua_file")"
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$lua_file"
        echo "✅ \$(basename "\$lua_file") 실행 완료"
    fi
done

# 월드별 Redis 스크립트 실행
for world_dir in "\$SQL_DIR"/UWO-*; do
    if [ -d "\$world_dir" ]; then
        world_name=\$(basename "\$world_dir")
        echo "🌍 \$world_name Redis 스크립트 실행 중..."
        for lua_file in "\$world_dir"/*_redis_update.lua; do
            if [ -f "\$lua_file" ]; then
                echo "📄 실행 중: \$world_name/\$(basename "\$lua_file")"
                redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$lua_file"
                echo "✅ \$world_name/\$(basename "\$lua_file") 실행 완료"
            fi
        done
    fi
done

echo "🎉 전체 데이터베이스 업데이트 완료!"
echo "📊 총 ${records.length}개 레코드가 업데이트되었습니다."
`;
        await fs_extra_1.default.writeFile(filePath, script, 'utf8');
        await fs_extra_1.default.chmod(filePath, 0o755); // 실행 권한 부여
        console.log(`📄 생성됨: ${fileName} (전체 업데이트 스크립트)`);
    }
    /**
     * 월드별 업데이트 스크립트 생성
     */
    async generateWorldUpdateScripts(records, worldMappings, scriptsDir, codebaseAnalysisResult) {
        // 월드별로 레코드 그룹화
        const worldGroups = new Map();
        records.forEach(record => {
            const worldId = record.gameServerId;
            if (!worldGroups.has(worldId)) {
                worldGroups.set(worldId, []);
            }
            worldGroups.get(worldId).push(record);
        });
        // 각 월드별로 스크립트 생성
        for (const [worldId, worldRecords] of worldGroups.entries()) {
            await this.generateSingleWorldUpdateScript(worldRecords, worldId, scriptsDir);
        }
    }
    /**
     * 단일 월드 업데이트 스크립트 생성
     */
    async generateSingleWorldUpdateScript(records, worldId, scriptsDir) {
        const fileName = `update_${worldId.toLowerCase().replace(/-/g, '_')}.sh`;
        const filePath = path_1.default.join(scriptsDir, fileName);
        let script = this.generateShellHeader(`${worldId} 월드 데이터베이스 업데이트`, records.length);
        script += `
# MySQL 연결 설정
MYSQL_HOST="\${MYSQL_HOST:-localhost}"
MYSQL_PORT="\${MYSQL_PORT:-3306}"
MYSQL_USER="\${MYSQL_USER:-root}"
MYSQL_PASSWORD="\${MYSQL_PASSWORD:-}"

# 스크립트 디렉토리
SCRIPT_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
SQL_DIR="\$(dirname "\$SCRIPT_DIR")/${worldId}"

echo "🌍 ${worldId} 월드 데이터베이스 업데이트 시작..."
echo "📁 SQL 스크립트 디렉토리: \$SQL_DIR"

# 에러 발생시 중단
set -e

# World 데이터베이스 업데이트
echo "🌍 World 데이터베이스 업데이트 중..."
if [ -f "\$SQL_DIR/world_update.sql" ]; then
    mysql -h"\$MYSQL_HOST" -P"\$MYSQL_PORT" -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" world < "\$SQL_DIR/world_update.sql"
    echo "✅ World 데이터베이스 업데이트 완료"
else
    echo "⚠️ world_update.sql 파일을 찾을 수 없습니다."
fi

# User Shard 데이터베이스 업데이트
for shard_file in "\$SQL_DIR"/user_shard_*_update.sql; do
    if [ -f "\$shard_file" ]; then
        shard_name=\$(basename "\$shard_file" .sql | sed 's/_update//')
        echo "👥 \$shard_name 데이터베이스 업데이트 중..."
        mysql -h"\$MYSQL_HOST" -P"\$MYSQL_PORT" -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" "\$shard_name" < "\$shard_file"
        echo "✅ \$shard_name 데이터베이스 업데이트 완료"
    fi
done

echo "🎉 ${worldId} 월드 데이터베이스 업데이트 완료!"
echo "📊 총 ${records.length}개 레코드가 업데이트되었습니다."
`;
        await fs_extra_1.default.writeFile(filePath, script, 'utf8');
        await fs_extra_1.default.chmod(filePath, 0o755); // 실행 권한 부여
        console.log(`📄 생성됨: ${fileName} (${worldId} 월드 업데이트 스크립트)`);
    }
    /**
     * Redis 전용 업데이트 스크립트 생성
     */
    async generateRedisUpdateScript(records, scriptsDir, codebaseAnalysisResult) {
        const fileName = 'update_redis.sh';
        const filePath = path_1.default.join(scriptsDir, fileName);
        let script = this.generateShellHeader('Redis 데이터 업데이트', records.length);
        script += `
# Redis 연결 설정
REDIS_HOST="\${REDIS_HOST:-localhost}"
REDIS_PORT="\${REDIS_PORT:-6379}"
REDIS_AUTH="\${REDIS_AUTH:-}"

# 스크립트 디렉토리
SCRIPT_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
SQL_DIR="\$(dirname "\$SCRIPT_DIR")"

echo "🔴 Redis 데이터 업데이트 시작..."
echo "📁 Lua 스크립트 디렉토리: \$SQL_DIR"

# 에러 발생시 중단
set -e

# Redis 연결 테스트
echo "🔍 Redis 연결 테스트 중..."
if [ -n "\$REDIS_AUTH" ]; then
    redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" ping > /dev/null
else
    redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" ping > /dev/null
fi
echo "✅ Redis 연결 성공"

# 전역 Redis 스크립트 실행
echo "🌐 전역 Redis 스크립트 실행 중..."

for lua_file in "\$SQL_DIR"/*_redis_update.lua; do
    if [ -f "\$lua_file" ]; then
        echo "📄 실행 중: \$(basename "\$lua_file")"
        if [ -n "\$REDIS_AUTH" ]; then
            redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" --eval "\$lua_file"
        else
            redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$lua_file"
        fi
        echo "✅ \$(basename "\$lua_file") 실행 완료"
    fi
done

# 월드별 Redis 스크립트 실행
for world_dir in "\$SQL_DIR"/UWO-*; do
    if [ -d "\$world_dir" ]; then
        world_name=\$(basename "\$world_dir")
        echo "🌍 \$world_name Redis 스크립트 실행 중..."
        for lua_file in "\$world_dir"/*_redis_update.lua; do
            if [ -f "\$lua_file" ]; then
                echo "📄 실행 중: \$world_name/\$(basename "\$lua_file")"
                if [ -n "\$REDIS_AUTH" ]; then
                    redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" --eval "\$lua_file"
                else
                    redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$lua_file"
                fi
                echo "✅ \$world_name/\$(basename "\$lua_file") 실행 완료"
            fi
        done
    fi
done

echo "🎉 Redis 데이터 업데이트 완료!"
echo "📊 총 ${records.length}개 레코드가 업데이트되었습니다."
`;
        await fs_extra_1.default.writeFile(filePath, script, 'utf8');
        await fs_extra_1.default.chmod(filePath, 0o755); // 실행 권한 부여
        console.log(`📄 생성됨: ${fileName} (Redis 업데이트 스크립트)`);
    }
    /**
     * Shell 스크립트 헤더 생성
     */
    generateShellHeader(description, recordCount) {
        const timestamp = new Date().toISOString();
        return `#!/bin/bash
# =====================================================
# UWO GNID/NID 리맵핑 Shell 스크립트
# 설명: ${description}
# 생성일시: ${timestamp}
# 레코드 수: ${recordCount}
# =====================================================

`;
    }
}
exports.ScriptGenerator = ScriptGenerator;
//# sourceMappingURL=scriptGenerator.js.map