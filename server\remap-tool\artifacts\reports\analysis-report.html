<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>코드베이스 분석 보고서</title>
    <style>
        
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f5f7fa;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
      }

      .timestamp {
        opacity: 0.9;
        font-size: 1.1rem;
      }

      .summary-section {
        margin-bottom: 40px;
      }

      .summary-section h2 {
        font-size: 1.8rem;
        margin-bottom: 20px;
        color: #2d3748;
      }

      .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      /* 새로운 카드 스타일 */
      .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .summary-card {
        background: white;
        padding: 25px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        text-align: left;
      }

      .summary-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      }

      .summary-icon {
        font-size: 2.5rem;
        margin-right: 20px;
        flex-shrink: 0;
      }

      .summary-content {
        flex: 1;
      }

      .summary-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 8px;
        line-height: 1;
      }

      .summary-label {
        color: #718096;
        font-weight: 500;
        font-size: 0.9rem;
      }

      .database-section, .redis-section, .redis-details-section {
        margin-bottom: 40px;
      }

      .database-section h2, .redis-section h2, .redis-details-section h2 {
        font-size: 1.8rem;
        margin-bottom: 20px;
        color: #2d3748;
      }

      /* 데이터베이스 테이블 상세 스타일 */
      .database-tables {
        display: flex;
        flex-direction: column;
        gap: 30px;
      }

      .database-table-detail {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e2e8f0;
      }

      .database-table-detail .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e2e8f0;
      }

      .database-table-detail .table-name {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 1.4rem;
        font-weight: bold;
        color: #2d3748;
        margin: 0;
      }

      .table-icon {
        font-size: 1.2rem;
      }

      .table-meta {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .database-table-detail .table-description {
        color: #718096;
        margin-bottom: 20px;
        font-style: italic;
      }

      .table-columns {
        margin-bottom: 25px;
      }

      .table-columns h4 {
        font-size: 1.1rem;
        color: #2d3748;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      /* 데이터베이스 컬럼 테이블 전용 스타일 */
      .columns-table-container {
        overflow-x: auto;
        margin: 15px 0;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        background: white;
      }

      .columns-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        margin: 0;
        font-size: 0.85rem;
        table-layout: fixed;
      }

      .columns-table thead {
        background: #2d3748;
        color: white;
      }

      .columns-table th {
        padding: 12px 16px;
        text-align: left;
        font-weight: 600;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
        border-right: 1px solid #4a5568;
        white-space: nowrap;
      }

      .columns-table th:last-child {
        border-right: none;
      }

      .columns-table td {
        padding: 12px 16px;
        border: none;
        border-right: 1px solid #e2e8f0;
        border-bottom: 1px solid #e2e8f0;
        vertical-align: middle;
      }

      .columns-table td:last-child {
        border-right: none;
      }

      .columns-table tbody tr:nth-child(even) {
        background-color: #f8f9fa;
      }

      .columns-table tbody tr:nth-child(odd) {
        background-color: white;
      }

      .columns-table tbody tr:hover {
        background-color: #e6fffa !important;
        transition: background-color 0.2s ease;
      }

      .columns-table tbody tr:hover td {
        background-color: transparent;
      }

      /* 컬럼 너비 설정 */
      .col-name { width: 25%; }
      .col-type { width: 20%; }
      .col-relation { width: 25%; }
      .col-attributes { width: 30%; }

      .column-name-code {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-weight: bold;
        color: #2d3748;
        background: #f7fafc;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
      }

      .column-type-code {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        color: #4a5568;
        background: #edf2f7;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
      }

      .col-relation {
        text-align: center;
      }

      .attributes-container {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        justify-content: flex-start;
      }

      .table-files {
        margin-top: 20px;
      }

      .table-files h4 {
        font-size: 1.1rem;
        color: #2d3748;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .file-list {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }

      .file-path {
        font-size: 0.8rem;
        color: #a0aec0;
        background: #f7fafc;
        padding: 6px 10px;
        border-radius: 4px;
        font-family: monospace;
        border-left: 3px solid #e2e8f0;
      }

      .file-path.more-files {
        color: #718096;
        font-style: italic;
        background: #edf2f7;
        border-left-color: #cbd5e0;
      }

      /* 테이블 요약 스타일 */
      .table-summary {
        margin-bottom: 20px;
      }

      .summary-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      /* 컬럼 속성 스타일 */
      .column-attributes {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }

      /* 인덱스 스타일 */
      .table-indexes {
        margin-top: 20px;
      }

      .table-indexes h4 {
        font-size: 1.1rem;
        color: #2d3748;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .index-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .index-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 8px 12px;
        background: #f7fafc;
        border-radius: 6px;
        border-left: 3px solid #e2e8f0;
      }

      .index-name {
        font-family: monospace;
        font-weight: bold;
        color: #2d3748;
      }

      .index-columns {
        font-family: monospace;
        color: #718096;
        font-size: 0.9rem;
      }

      .table-grid, .redis-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
      }

      .table-card, .redis-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
      }

      .table-card:hover, .redis-card:hover {
        transform: translateY(-2px);
      }

      .table-header, .redis-header {
        margin-bottom: 15px;
      }

      .table-name, .redis-pattern {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2d3748;
        margin-bottom: 10px;
        word-break: break-all;
      }

      .table-badges, .redis-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
      }

      .badge {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
        display: inline-block;
        white-space: nowrap;
        line-height: 1.2;
      }

      .badge-account { background: #fed7d7; color: #c53030; }
      .badge-pub { background: #bee3f8; color: #2b6cb0; }
      .badge-user { background: #c6f6d5; color: #2f855a; }
      .badge-gnid { background: #fbb6ce; color: #b83280; }
      .badge-nid { background: #faf089; color: #744210; }
      .badge-shard { background: #e6fffa; color: #234e52; }
      .badge-primary { background: #fed7d7; color: #c53030; }
      .badge-index { background: #bee3f8; color: #2b6cb0; }
      .badge-notnull { background: #c6f6d5; color: #2f855a; }
      .badge-unique { background: #fbb6ce; color: #b83280; }

      .table-info {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 10px;
      }

      .database-type {
        font-size: 0.8rem;
        color: #4a5568;
        background: #edf2f7;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 500;
      }

      .redis-instance {
        font-size: 0.8rem;
        color: #4a5568;
        margin-bottom: 5px;
      }

      .table-description, .redis-description {
        color: #718096;
        margin-bottom: 10px;
        font-size: 0.9rem;
      }

      .table-files {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }

      .file-path {
        font-size: 0.8rem;
        color: #a0aec0;
        background: #f7fafc;
        padding: 4px 8px;
        border-radius: 4px;
        font-family: monospace;
      }

      .no-data {
        text-align: center;
        color: #a0aec0;
        font-style: italic;
        padding: 40px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      }

      /* 테이블 스타일 */
      .table-container {
        overflow-x: auto;
        margin: 20px 0;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e2e8f0;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        margin: 0;
        border-spacing: 0;
      }

      thead {
        background: #4a5568;
        color: white;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      th {
        padding: 12px 16px;
        text-align: left;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
        border-right: 1px solid #718096;
      }

      th:last-child {
        border-right: none;
      }

      td {
        padding: 12px 16px;
        text-align: left;
        font-size: 0.9rem;
        border: none;
        border-right: 1px solid #e2e8f0;
        border-bottom: 1px solid #e2e8f0;
        vertical-align: top;
      }

      td:last-child {
        border-right: none;
      }

      /* 번갈아가는 행 색상 (zebra striping) */
      tbody tr:nth-child(even) {
        background-color: #f8f9fa;
      }

      tbody tr:nth-child(odd) {
        background-color: white;
      }

      tbody tr:hover {
        background-color: #e6fffa !important;
        transition: background-color 0.2s ease;
      }

      tbody tr:hover td {
        background-color: transparent;
      }

      /* 상태 표시 스타일 */
      .success {
        color: #38a169;
        font-weight: 600;
      }

      .error {
        color: #e53e3e;
        font-weight: 600;
      }

      .warning {
        color: #d69e2e;
        font-weight: 600;
      }

      /* 키 타입 스타일 */
      .key-type {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      .key-type-string { background: #e6fffa; color: #234e52; }
      .key-type-hash { background: #fef5e7; color: #744210; }
      .key-type-list { background: #e6f3ff; color: #1a365d; }
      .key-type-set { background: #f0fff4; color: #22543d; }
      .key-type-zset { background: #faf5ff; color: #553c9a; }
      .key-type-stream { background: #fff5f5; color: #742a2a; }
      .key-type-none { background: #f7fafc; color: #718096; }
      .key-type-unknown { background: #fed7d7; color: #742a2a; }

      /* ID 배지 컨테이너 */
      .id-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }

      /* 오류 및 경고 목록 스타일 */
      .error-list, .warning-list {
        margin: 20px 0;
      }

      .error-item, .warning-item {
        padding: 12px 16px;
        margin: 8px 0;
        border-radius: 6px;
        font-family: monospace;
        font-size: 0.9rem;
        line-height: 1.4;
      }

      .error-item {
        background: #fed7d7;
        border-left: 4px solid #e53e3e;
        color: #742a2a;
      }

      .warning-item {
        background: #fefcbf;
        border-left: 4px solid #d69e2e;
        color: #744210;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .header h1 {
          font-size: 2rem;
        }

        .summary-grid {
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }

        .summary-cards {
          grid-template-columns: 1fr;
        }

        .summary-card {
          flex-direction: column;
          text-align: center;
        }

        .summary-icon {
          margin-right: 0;
          margin-bottom: 10px;
        }

        .table-grid, .redis-grid {
          grid-template-columns: 1fr;
        }

        th, td {
          padding: 8px 12px;
          font-size: 0.8rem;
        }

        .table-container {
          font-size: 0.8rem;
        }
      }
    
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🔍 코드베이스 분석 보고서</h1>
            <p class="timestamp">생성일시: 2025. 7. 25. 오후 6:23:08</p>
        </header>

        <div class="summary-section">
            <h2>📊 분석 요약</h2>
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-icon">📁</div>
                    <div class="summary-content">
                        <div class="summary-number">4,792</div>
                        <div class="summary-label">총 분석 파일</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">🗄️</div>
                    <div class="summary-content">
                        <div class="summary-number">5</div>
                        <div class="summary-label">데이터베이스 테이블</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">🔑</div>
                    <div class="summary-content">
                        <div class="summary-number">5</div>
                        <div class="summary-label">Redis 키 패턴</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">⏱️</div>
                    <div class="summary-content">
                        <div class="summary-number">6.74s</div>
                        <div class="summary-label">분석 시간</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="database-section">
            <h2>🗄️ 데이터베이스 테이블 (5개)</h2>
            <div class="database-tables">
      
      <div class="database-table-detail">
        <div class="table-header">
          <h3 class="table-name">
            <span class="table-icon">🗄️</span>
            w_auto_sailings
          </h3>
          <div class="table-meta">
            <span class="database-type">world</span>
            
          </div>
        </div>

        <div class="table-summary">
          <div class="summary-badges">
            <span class="badge badge-account">AccountID 사용</span>
            <span class="badge badge-pub">PubID 사용</span>
          </div>
        </div>

        <div class="table-columns">
          <h4>📋 컬럼 정보 (11개)</h4>
          <div class="columns-table-container">
            <table class="columns-table">
              <thead>
                <tr>
                  <th class="col-name">컬럼명</th>
                  <th class="col-type">데이터 타입</th>
                  <th class="col-relation">관련 ID</th>
                  <th class="col-attributes">속성</th>
                </tr>
              </thead>
              <tbody>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">userId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        <span class="badge badge-primary">PK</span>
                        <span class="badge badge-index">IDX</span>
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">fleetIndex</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        <span class="badge badge-primary">PK</span>
                        <span class="badge badge-index">IDX</span>
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">accountId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(32)</code>
                    </td>
                    <td class="col-relation">
                      <span class="badge badge-account">AccountID (GNID)</span>
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">pubId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(32)</code>
                    </td>
                    <td class="col-relation">
                      <span class="badge badge-pub">PubID (NID)</span>
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">startTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">destCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">destType</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">path</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">JSON</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">isOfflineSailingDeactivated</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">extra</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">JSON</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">optionForServer</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">JSON</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
              </tbody>
            </table>
          </div>
        </div>

        
        <div class="table-indexes">
          <h4>🔍 인덱스 정보 (1개)</h4>
          <div class="index-list">
            
              <div class="index-item">
                <span class="index-name">PRIMARY</span>
                <span class="index-columns">(userId, fleetIndex)</span>
                <span class="badge badge-unique">UNIQUE</span>
              </div>
            
          </div>
        </div>
        
      </div>
    
      <div class="database-table-detail">
        <div class="table-header">
          <h3 class="table-name">
            <span class="table-icon">🗄️</span>
            u_users
          </h3>
          <div class="table-meta">
            <span class="database-type">user</span>
            <span class="badge badge-shard">샤딩 필요</span>
          </div>
        </div>

        <div class="table-summary">
          <div class="summary-badges">
            
            <span class="badge badge-pub">PubID 사용</span>
          </div>
        </div>

        <div class="table-columns">
          <h4>📋 컬럼 정보 (46개)</h4>
          <div class="columns-table-container">
            <table class="columns-table">
              <thead>
                <tr>
                  <th class="col-name">컬럼명</th>
                  <th class="col-type">데이터 타입</th>
                  <th class="col-relation">관련 ID</th>
                  <th class="col-attributes">속성</th>
                </tr>
              </thead>
              <tbody>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">id</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        <span class="badge badge-primary">PK</span>
                        <span class="badge badge-index">IDX</span>
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">pubId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(32)</code>
                    </td>
                    <td class="col-relation">
                      <span class="badge badge-pub">PubID (NID)</span>
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">createTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">name</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(40)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lang</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(8)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">nationCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">companyJobCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastRewardedAchievementPointCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">noviceSupplyCount</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastNoviceSupplyTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">energy</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastUpdateEnergyTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">usedExploreQuickModeCount</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastExploreQuickModeCountUpdateTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">usedExploreTicketCount</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastExploreTicketCountUpdateTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">palaceRoyalOrderCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">palaceRoyalOrderRnds</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(40)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastRoyalOrderCompletedTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">palaceRoyalTitleOrderCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">palaceRoyalTitleOrderRnds</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(40)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">contractedCollectorTownBuildingCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">guildId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastGuildLeftTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastCompanyJobUpdateTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">countryIp</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(5)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastReceiveHotTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">firstMateCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastUpdateNationTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">isAdmiralProfileOpened</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">isFlagShipProfileOpened</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">westShipBuildLevel</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">westShipBuildExp</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastCashShopDailyProductsUpdateTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">freeLeaderMateSwitchCount</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">freeLastLeaderMateSwitchTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">orientShipBuildLevel</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">orientShipBuildExp</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">accumInvestByGem</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">BIGINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">curCargoPresetId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">primeMinisterElectedCount</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">representedMateCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastPaidSmuggleEnterTownCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastSmuggleTransactionTownCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">isFirstFleetProfileOpened</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">isFriendlyBattleRequestable</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
              </tbody>
            </table>
          </div>
        </div>

        
        <div class="table-indexes">
          <h4>🔍 인덱스 정보 (1개)</h4>
          <div class="index-list">
            
              <div class="index-item">
                <span class="index-name">PRIMARY</span>
                <span class="index-columns">(id)</span>
                <span class="badge badge-unique">UNIQUE</span>
              </div>
            
          </div>
        </div>
        
      </div>
    
      <div class="database-table-detail">
        <div class="table-header">
          <h3 class="table-name">
            <span class="table-icon">🗄️</span>
            a_accounts
          </h3>
          <div class="table-meta">
            <span class="database-type">auth</span>
            
          </div>
        </div>

        <div class="table-summary">
          <div class="summary-badges">
            <span class="badge badge-account">AccountID 사용</span>
            
          </div>
        </div>

        <div class="table-columns">
          <h4>📋 컬럼 정보 (12개)</h4>
          <div class="columns-table-container">
            <table class="columns-table">
              <thead>
                <tr>
                  <th class="col-name">컬럼명</th>
                  <th class="col-type">데이터 타입</th>
                  <th class="col-relation">관련 ID</th>
                  <th class="col-attributes">속성</th>
                </tr>
              </thead>
              <tbody>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">id</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(32)</code>
                    </td>
                    <td class="col-relation">
                      <span class="badge badge-account">AccountID (GNID)</span>
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastWorldId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(32)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">isOnline</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastLobby</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(64)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastUserId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">createTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">lastLoginTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">accessLevel</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TINYINT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">blockTimeUtcByAdmin</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">revokeTimeUtc</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">TIMESTAMP</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">loginPlatform</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(10)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">clientVersion</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(15)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
              </tbody>
            </table>
          </div>
        </div>

        
      </div>
    
      <div class="database-table-detail">
        <div class="table-header">
          <h3 class="table-name">
            <span class="table-icon">🗄️</span>
            a_pub_ids
          </h3>
          <div class="table-meta">
            <span class="database-type">auth</span>
            
          </div>
        </div>

        <div class="table-summary">
          <div class="summary-badges">
            <span class="badge badge-account">AccountID 사용</span>
            <span class="badge badge-pub">PubID 사용</span>
          </div>
        </div>

        <div class="table-columns">
          <h4>📋 컬럼 정보 (3개)</h4>
          <div class="columns-table-container">
            <table class="columns-table">
              <thead>
                <tr>
                  <th class="col-name">컬럼명</th>
                  <th class="col-type">데이터 타입</th>
                  <th class="col-relation">관련 ID</th>
                  <th class="col-attributes">속성</th>
                </tr>
              </thead>
              <tbody>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">accountId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(32)</code>
                    </td>
                    <td class="col-relation">
                      <span class="badge badge-account">AccountID (GNID)</span>
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">pubId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(32)</code>
                    </td>
                    <td class="col-relation">
                      <span class="badge badge-pub">PubID (NID)</span>
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">worldId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(32)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
              </tbody>
            </table>
          </div>
        </div>

        
      </div>
    
      <div class="database-table-detail">
        <div class="table-header">
          <h3 class="table-name">
            <span class="table-icon">🗄️</span>
            a_world_users
          </h3>
          <div class="table-meta">
            <span class="database-type">auth</span>
            
          </div>
        </div>

        <div class="table-summary">
          <div class="summary-badges">
            
            <span class="badge badge-pub">PubID 사용</span>
          </div>
        </div>

        <div class="table-columns">
          <h4>📋 컬럼 정보 (5개)</h4>
          <div class="columns-table-container">
            <table class="columns-table">
              <thead>
                <tr>
                  <th class="col-name">컬럼명</th>
                  <th class="col-type">데이터 타입</th>
                  <th class="col-relation">관련 ID</th>
                  <th class="col-attributes">속성</th>
                </tr>
              </thead>
              <tbody>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">userId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        <span class="badge badge-primary">PK</span>
                        <span class="badge badge-index">IDX</span>
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">pubId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(32)</code>
                    </td>
                    <td class="col-relation">
                      <span class="badge badge-pub">PubID (NID)</span>
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        <span class="badge badge-index">IDX</span>
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">name</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(40)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        <span class="badge badge-index">IDX</span>
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">worldId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">VARCHAR(32)</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        
                      </div>
                    </td>
                  </tr>
                
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">nationCmsId</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">INT</code>
                    </td>
                    <td class="col-relation">
                      -
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        
                        
                        <span class="badge badge-notnull">NOT NULL</span>
                      </div>
                    </td>
                  </tr>
                
              </tbody>
            </table>
          </div>
        </div>

        
        <div class="table-indexes">
          <h4>🔍 인덱스 정보 (3개)</h4>
          <div class="index-list">
            
              <div class="index-item">
                <span class="index-name">IDX_a_world_users__pubId</span>
                <span class="index-columns">(pubId)</span>
                
              </div>
            
              <div class="index-item">
                <span class="index-name">PRIMARY</span>
                <span class="index-columns">(userId)</span>
                <span class="badge badge-unique">UNIQUE</span>
              </div>
            
              <div class="index-item">
                <span class="index-name">UQ_a_world_users__name_worldId</span>
                <span class="index-columns">(name, worldId)</span>
                <span class="badge badge-unique">UNIQUE</span>
              </div>
            
          </div>
        </div>
        
      </div>
    
    </div>
        </div>

        <div class="redis-details-section">
            <h2>🔑 Redis 키 패턴 (5개)</h2>
            
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>키 패턴</th>
              <th>타입</th>
              <th>Redis 인스턴스</th>
              <th>설명</th>
              <th>사용하는 ID</th>
            </tr>
          </thead>
          <tbody>
            
              <tr>
                <td><code>account:{accountId}</code></td>
                <td><span class="key-type key-type-hash">Hash</span></td>
                <td>userCache</td>
                <td>계정 정보 저장 (토큰, 월드ID, 주문ID, 하트비트 등)</td>
                <td>
                  <div class="id-badges">
                    <span class="badge badge-account">ACCOUNTID(GNID)</span>
                  </div>
                </td>
              </tr>
            
              <tr>
                <td><code>prologueGnids:{worldId}</code></td>
                <td><span class="key-type key-type-zset">Sorted Set</span></td>
                <td>order</td>
                <td>월드별 프롤로그 상태 유저 관리 (Sorted Set, gnid를 member로 사용)</td>
                <td>
                  <div class="id-badges">
                    <span class="badge badge-gnid">GNID</span>
                  </div>
                </td>
              </tr>
            
              <tr>
                <td><code>deletionPubIds</code></td>
                <td><span class="key-type key-type-list">List</span></td>
                <td>auth</td>
                <td>삭제 대상 pubId 목록 관리 (List)</td>
                <td>
                  <div class="id-badges">
                    <span class="badge badge-pub">PUBID(NID)</span>
                  </div>
                </td>
              </tr>
            
              <tr>
                <td><code>townUserWeeklyInvestmentReport:{nid}</code></td>
                <td><span class="key-type key-type-string">String</span></td>
                <td>userCache</td>
                <td>도시 주간 투자 보고서 (nid 기반 캐시)</td>
                <td>
                  <div class="id-badges">
                    <span class="badge badge-pub">PUBID(NID)</span>
                  </div>
                </td>
              </tr>
            
              <tr>
                <td><code>user:{userId}</code></td>
                <td><span class="key-type key-type-hash">Hash</span></td>
                <td>userCache</td>
                <td>유저 캐시 정보 (Hash 내부 pubId 필드 값 변경)</td>
                <td>
                  <div class="id-badges">
                    <span class="badge badge-pub">PUBID(NID)</span><span class="badge badge-user">USERID</span>
                  </div>
                </td>
              </tr>
            
          </tbody>
        </table>
      </div>
    
        </div>
    </div>
</body>
</html>