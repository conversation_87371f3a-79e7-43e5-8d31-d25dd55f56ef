{
  // UWO Remapping Tool Configuration
  // This file uses JSON5 format which supports comments and trailing commas

  "sharedConfig": {
    // Auth Database Configuration
    "mysqlAuthDb": {
      "host": "localhost",
      "port": 3306,
      "user": "motif_dev",
      "password": "dev123$",
      "database": "uwo_auth",
      "multipleStatements": true,
      "supportBigNumbers": true,
      "bigNumberStrings": true,
      "connectTimeout": 3000,
      "connectionLimit": 10,
      "flags": "-FOUND_ROWS",
      "driver": "mysql"
    },
    // Auth Redis Configuration
    "authRedis": {
      "redisCfg": {
        "host": "localhost",
        "port": 6379,
        "db": 19
      },
      "scriptDir": "auth",
      "pool": {
        "min": 1,
        "max": 4
      }
    },
    "monitorRedis": {
      "redisCfg": {
        "host": "localhost",
        "port": 6379,
        "db": 3
      },
      "scriptDir": "monitor",
      "pool": {
        "min": 1,
        "max": 4
      }
    },
    "orderRedis": {
      "redisCfg": {
        "host": "localhost",
        "port": 6379,
        "db": 18
      },
      "scriptDir": "order",
      "pool": {
        "min": 1,
        "max": 4
      }
    },
    "globalMatchRedis": {
      "redisCfg": {
        "host": "localhost",
        "port": 6379,
        "db": 23
      },
      "scriptDir": "globalMatch",
      "pool": {
        "min": 1,
        "max": 4
      }
    },
    "globalBattleLogRedis": {
      "redisCfg": {
        "host": "localhost",
        "port": 6379,
        "db": 24
      },
      "scriptDir": "battleLog",
      "pool": {
        "min": 1,
        "max": 4
      }
    }
  },

  // World Configurations
  "worlds": [
    {
      "id": "UWO-KR-04", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-AU-01", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-KR-10", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-KR-01", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-KR-03", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-KR-09", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-KR-08", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-KR-06", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-KR-05", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-KR-07", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-KR-12", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-KR-02", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
    {
      "id": "UWO-AU-02", // World identifier
      "mysqlUserDb": {
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "townRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 11
        },
        "scriptDir": "town",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "collectorRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 4
        },
        "scriptDir": "collector",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "sailRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 15
        },
        "scriptDir": "sail",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "guildRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 16
        },
        "scriptDir": "guild",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "arenaRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 17
        },
        "scriptDir": "arena",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "raidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 20
        },
        "scriptDir": "raid",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "rankingRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 21
        },
        "scriptDir": "ranking",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "userRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 9
        },
        "scriptDir": "user",
        "pool": {
          "min": 1,
          "max": 4
        }
      },
      "townLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 14
        },
        "pool": {
          "min": 2,
          "max": 4
        },
        "scriptDir": "zoneLb"
      },
      "oceanLbRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 5
        },
        "pool": {
          "min": 2,
          "max": 2
        },
        "scriptDir": "zoneLb"
      },
      "blindBidRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 22
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "blindBid"
      }
    },
  ]
}
