import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';
import { CliOptions } from '../types';
import { ConfigLoader } from '../config/configLoader';
import { RemapDataSummaryAnalyzer, RemapRecord } from '../analyzer/remapDataSummaryAnalyzer';
import { DatabaseScriptGenerator } from './databaseScriptGenerator';
import { RedisScriptGenerator } from './redisScriptGenerator';
import { CodebaseAnalyzer } from '../analyzer/codebaseAnalyzer';

export class ScriptGenerator {
  private configLoader: ConfigLoader;
  private analyzer: RemapDataSummaryAnalyzer;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private dbScriptGenerator: DatabaseScriptGenerator;
  private redisScriptGenerator: RedisScriptGenerator;

  constructor() {
    this.configLoader = new ConfigLoader();
    this.analyzer = new RemapDataSummaryAnalyzer();
    this.codebaseAnalyzer = new CodebaseAnalyzer();
    this.dbScriptGenerator = new DatabaseScriptGenerator();
    this.redisScriptGenerator = new RedisScriptGenerator();
  }

  async generateScripts(options: CliOptions): Promise<void> {
    console.log('📄 CSV 파일 읽는 중:', options.csvFile);

    // scripts 폴더 비우기
    console.log('🗑️ 기존 스크립트 폴더 정리 중...');
    await this.cleanScriptsDirectory(options.outputDir);

    // 1단계: 코드베이스 분석 수행 (항상 최신 상태로)
    console.log('🔍 코드베이스 분석 수행 중...');

    // 스키마 디렉토리 확인 및 자동 추출
    const schemaDir = options.schemaDir || './artifacts/schemas';
    const schemaExists = await fs.pathExists(schemaDir);

    if (!schemaExists) {
      console.log(chalk.yellow('⚠️ 스키마 디렉토리가 없습니다.'));
      console.log(chalk.cyan('🔄 스키마 자동 추출을 시도합니다...'));

      try {
        await this.extractSchemaIfPossible(schemaDir);
        console.log(chalk.green('✅ 스키마 추출 완료'));
      } catch (error) {
        console.log(chalk.yellow('⚠️ 스키마 자동 추출 실패. 기본 분석을 수행합니다.'));
        console.log(chalk.yellow('💡 더 정확한 분석을 위해 "extract-schema" 명령을 수동으로 실행하세요.'));
      }
    }

    const finalSchemaDir = await fs.pathExists(schemaDir) ? schemaDir : undefined;
    const codebaseAnalysisResult = await this.codebaseAnalyzer.analyzeCodebase(finalSchemaDir);
    console.log(`📊 분석 완료: ${codebaseAnalysisResult.databaseTables.length}개 테이블, ${codebaseAnalysisResult.redisKeys.length}개 Redis 패턴`);

    // 테이블이 없으면 경고 표시
    if (codebaseAnalysisResult.databaseTables.length === 0) {
      console.log(chalk.yellow('⚠️ 분석된 데이터베이스 테이블이 없습니다.'));
      console.log(chalk.yellow('💡 다음 중 하나를 시도해보세요:'));
      console.log(chalk.yellow('   1. extract-schema 명령으로 스키마를 먼저 추출'));
      console.log(chalk.yellow('   2. 데이터베이스 연결 설정 확인'));
      console.log(chalk.yellow('   3. 테이블 이름이 올바른 prefix(a_, w_, u_)를 가지는지 확인'));
    }

    // 분석 결과 저장
    await this.saveAnalysisResult(codebaseAnalysisResult, options.outputDir);

    // 2단계: CSV 파일 분석
    const analysisResult = await this.analyzer.analyzeCSV(options.csvFile);
    console.log(`📊 ${analysisResult.totalRecords}개 레코드 분석 완료`);

    // 설정 로드
    this.configLoader = new ConfigLoader(options.configDir);
    const config = await this.configLoader.loadRemapToolConfig();

    // 월드별 매핑 생성
    const worldMappings = this.configLoader.generateWorldShardMapping(config);

    // 출력 디렉토리 생성
    await fs.ensureDir(options.outputDir);

    // 1단계: 전역 스크립트 생성 (auth, world, userCache, order)
    await this.generateGlobalScripts(options, analysisResult.records, codebaseAnalysisResult);

    // 2단계: 월드별 스크립트 생성 (user 샤드)
    if (options.worldId) {
      // 특정 월드만 처리
      await this.generateWorldScripts(options, analysisResult.records, worldMappings, options.worldId, codebaseAnalysisResult);
    } else {
      // 모든 월드 처리
      const worldIds = [...new Set(analysisResult.records.map(r => r.gameServerId))];

      for (const worldId of worldIds) {
        await this.generateWorldScripts(options, analysisResult.records, worldMappings, worldId, codebaseAnalysisResult);
      }
    }

    console.log(chalk.green('✅ 모든 스크립트 생성 완료'));
  }

  /**
   * 스키마 자동 추출 시도
   */
  private async extractSchemaIfPossible(schemaDir: string): Promise<void> {
    try {
      // 설정 로드
      const config = await this.configLoader.loadRemapToolConfig();

      // 스키마 추출기 생성 및 실행
      const { SchemaExtractor, saveSchemaToFile } = await import('../extractor/schemaExtractor');
      const extractor = new SchemaExtractor(config);

      // 추출할 데이터베이스 목록 정의
      const extractors = [
        { type: 'auth' as const, name: 'Auth', dbName: config.sharedConfig.mysqlAuthDb.database },
        { type: 'world' as const, name: 'World', dbName: config.worlds[0]?.mysqlWorldDb?.database },
        { type: 'user' as const, name: 'User', dbName: config.worlds[0]?.mysqlUserDb?.sqlDefaultCfg?.database }
      ];

      // 각 데이터베이스별로 스키마 추출
      for (const { type, name, dbName } of extractors) {
        if (!dbName) continue;

        try {
          console.log(`📊 ${name} 스키마 추출 중...`);

          await extractor.connect(type, type === 'user' ? '0' : undefined);
          const schema = await extractor.extractSchema(type, dbName);
          await saveSchemaToFile(schema, schemaDir);
          await extractor.disconnect();

          console.log(`✅ ${name} 스키마 추출 완료 (${schema.tables.length}개 테이블)`);
        } catch (error) {
          console.warn(`⚠️ ${name} 스키마 추출 실패:`, error);
        }
      }
    } catch (error) {
      throw new Error(`스키마 추출 실패: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 분석 결과를 파일로 저장
   */
  private async saveAnalysisResult(analysisResult: any, outputDir: string): Promise<void> {
    try {
      // artifacts/reports 디렉토리 생성
      const reportsDir = path.join('./artifacts/reports');
      await fs.ensureDir(reportsDir);

      // JSON 형식으로 저장 (기존 호환성 유지)
      const jsonPath = path.join(reportsDir, 'analysis-result.json');
      await fs.writeFile(jsonPath, JSON.stringify(analysisResult, null, 2), 'utf8');
      console.log(`📊 분석 결과 저장: ${jsonPath}`);

      // 추가로 outputDir에도 저장 (JSON5 형식)
      const outputJsonPath = path.join(outputDir, 'analysis-result.json5');
      await fs.writeFile(outputJsonPath, JSON.stringify(analysisResult, null, 2), 'utf8');
      console.log(`📊 분석 결과 저장: ${outputJsonPath}`);
    } catch (error) {
      console.warn(`⚠️ 분석 결과 저장 실패: ${error}`);
    }
  }

  private async generateGlobalScripts(
    options: CliOptions,
    records: RemapRecord[],
    codebaseAnalysisResult: any
  ): Promise<void> {
    console.log('🌐 전역 스크립트 생성 중 (auth, userCache, order)...');

    // 전역 SQL 스크립트 생성 (auth만 - world는 월드별)
    await this.dbScriptGenerator.generateGlobalScripts(records, options.outputDir, codebaseAnalysisResult);

    // 전역 Redis 스크립트 생성 (auth, userCache, order)
    await this.redisScriptGenerator.generateGlobalScripts(records, options.outputDir, codebaseAnalysisResult);

    console.log('✅ 전역 스크립트 생성 완료');
  }

  private async generateWorldScripts(
    options: CliOptions,
    records: RemapRecord[],
    worldMappings: any[],
    worldId: string,
    codebaseAnalysisResult: any
  ): Promise<void> {
    console.log(`🌍 ${worldId} 월드 스크립트 생성 중...`);
    
    // 해당 월드의 레코드만 필터링
    const worldRecords = records.filter(r => r.gameServerId === worldId);
    
    if (worldRecords.length === 0) {
      console.log(chalk.yellow(`⚠️ ${worldId} 월드에 대한 레코드가 없습니다.`));
      return;
    }

    // 월드별 출력 디렉토리 생성
    const worldOutputDir = path.join(options.outputDir, worldId);
    await fs.ensureDir(worldOutputDir);

    // 월드 매핑 찾기 (설정이 없으면 기본값 사용)
    let worldMapping = this.configLoader.getShardForWorld(worldId, worldMappings);
    if (!worldMapping) {
      console.log(chalk.yellow(`⚠️ ${worldId} 월드에 대한 설정을 찾을 수 없습니다. 기본 설정을 사용합니다.`));
      worldMapping = {
        worldId: worldId,
        shardId: 0,
        userDatabase: `user_${worldId}`,
        worldDatabase: `world_${worldId}`
      };
    }

    // 월드별 SQL 스크립트 생성 (user 샤드 + world 데이터베이스)
    await this.dbScriptGenerator.generateUserShardScripts(worldRecords, worldMapping, worldOutputDir, codebaseAnalysisResult);
    await this.dbScriptGenerator.generateWorldDatabaseScript(worldRecords, worldMapping, worldOutputDir, codebaseAnalysisResult);

    console.log(`✅ ${worldId} 월드 스크립트 생성 완료 (${worldRecords.length}개 레코드)`);
  }



  private async cleanScriptsDirectory(outputDir: string): Promise<void> {
    try {
      if (await fs.pathExists(outputDir)) {
        await fs.remove(outputDir);
        console.log(`✅ 기존 스크립트 폴더 삭제 완료: ${outputDir}`);
      }
      await fs.ensureDir(outputDir);
      console.log(`📁 새로운 스크립트 폴더 생성: ${outputDir}`);
    } catch (error) {
      console.log(chalk.yellow(`⚠️ 스크립트 폴더 정리 중 오류: ${error}`));
    }
  }
}
