const { CodebaseAnalyzer } = require('./dist/analyzer/codebaseAnalyzer');

async function testAnalysis() {
  console.log('🔍 코드베이스 분석 테스트 시작...');
  
  try {
    const analyzer = new CodebaseAnalyzer();
    const result = await analyzer.analyzeCodebase();
    
    console.log(`📊 분석 결과:`);
    console.log(`- 총 파일 수: ${result.totalFilesAnalyzed}`);
    console.log(`- SQL 파일: ${result.sqlFilesAnalyzed}`);
    console.log(`- Lua 파일: ${result.luaFilesAnalyzed}`);
    console.log(`- TypeScript 파일: ${result.tsFilesAnalyzed}`);
    console.log(`- 데이터베이스 테이블: ${result.databaseTables.length}`);
    console.log(`- Redis 키 패턴: ${result.redisKeys.length}`);
    
    if (result.redisKeys.length > 0) {
      console.log(`\n📋 Redis 키 패턴 예시 (처음 5개):`);
      result.redisKeys.slice(0, 5).forEach((key, index) => {
        console.log(`  ${index + 1}. ${key.keyPattern} (${key.redisInstance})`);
      });
    }
    
    console.log(`\n✅ 분석 완료! (${result.analysisTime}ms)`);
    
  } catch (error) {
    console.error('❌ 분석 실패:', error);
  }
}

testAnalysis();
