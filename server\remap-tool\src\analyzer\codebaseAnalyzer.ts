import fs from 'fs-extra';
import path from 'path';
import { glob } from 'glob';
import * as ts from 'typescript';
import * as luaparse from 'luaparse';
import JSON5 from 'json5';
import { DatabaseTableInfo, RedisKeyInfo, AnalysisResult, DatabaseColumnInfo, DatabaseIndexInfo } from '../types';
import { getAllRedisKeyPatterns } from '../config/redisKeyPatterns';

/**
 * 마이그레이션 기반 스키마 빌더 (정규식 기반)
 */
class MigrationSchemaBuilder {
  private tables = new Map<string, DatabaseTableInfo>();

  constructor() {
    // 생성자 단순화
  }

  /**
   * 마이그레이션 파일 적용
   */
  async applyMigration(filePath: string): Promise<void> {
    const content = await fs.readFile(filePath, 'utf-8');

    try {
      // CREATE TABLE 처리
      await this.processCreateTables(content, filePath);

      // ALTER TABLE 처리
      await this.processAlterTables(content, filePath);

      // DROP TABLE 처리
      await this.processDropTables(content);

    } catch (error) {
      console.log(`[마이그레이션 파일 처리 실패] ${path.basename(filePath)}: ${error}`);
    }
  }

  /**
   * CREATE TABLE 문 처리
   */
  private async processCreateTables(content: string, filePath: string): Promise<void> {
    // 먼저 CREATE TABLE 구문들을 찾아서 개별적으로 처리
    const createTableMatches = content.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?\w+`?[\s\S]*?\)\s*(?:ENGINE[\s\S]*?)?;/gi);

    if (!createTableMatches) return;

    for (const createTableStatement of createTableMatches) {
      // 테이블명 추출
      const tableNameMatch = createTableStatement.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?/i);
      if (!tableNameMatch || !tableNameMatch[1]) continue;

      const tableName = tableNameMatch[1];

      // 테이블 정의 추출 (괄호 안의 내용)
      const definitionMatch = createTableStatement.match(/\(([\s\S]*)\)\s*(?:ENGINE|$)/i);
      if (!definitionMatch || !definitionMatch[1]) continue;

      const tableDefinition = definitionMatch[1];

      const database = this.determineDatabaseType(filePath);
      if (!this.isValidTableName(tableName, database)) {
        continue;
      }

      const tableInfo = this.createTableFromDefinition(tableName, tableDefinition, filePath);
      if (tableInfo) {
        this.tables.set(tableName, tableInfo);
        console.log(`[CREATE] 테이블 생성: ${tableName} (${tableInfo.columns.length}개 컬럼)`);
      }
    }
  }

  /**
   * ALTER TABLE 문 처리
   */
  private async processAlterTables(content: string, filePath: string): Promise<void> {
    // ADD COLUMN
    const addColumnRegex = /ALTER\s+TABLE\s+`?(\w+)`?\s+ADD\s+(?:COLUMN\s+)?`?(\w+)`?\s+([^,;]+?)(?:,|\s*;|\s*$)/gi;
    let match;

    while ((match = addColumnRegex.exec(content)) !== null) {
      const tableName = match[1];
      const columnName = match[2];
      const columnDef = match[3];

      if (!tableName || !columnName || !columnDef) continue;

      // INDEX/KEY 관련 구문 제외
      if (columnName.toUpperCase() === 'INDEX' ||
          columnName.toUpperCase() === 'KEY' ||
          columnDef.toUpperCase().includes('INDEX') ||
          columnDef.toUpperCase().includes('KEY')) {
        continue;
      }

      const database = this.determineDatabaseType(filePath);
      if (!this.isValidTableName(tableName, database)) {
        continue;
      }

      let table = this.tables.get(tableName);
      if (!table) {
        table = this.createEmptyTableInfo(tableName, filePath);
        this.tables.set(tableName, table);
      }

      const columnInfo = this.parseColumnDefinition(columnName, columnDef.trim(), tableName);
      if (columnInfo && !table.columns.find(c => c.columnName === columnName)) {
        table.columns.push(columnInfo);
        this.updateTableFlags(table, columnInfo);
        console.log(`[ALTER ADD] ${tableName}.${columnName} 컬럼 추가`);
      }
    }

    // DROP COLUMN
    const dropColumnRegex = /ALTER\s+TABLE\s+`?(\w+)`?\s+DROP\s+(?:COLUMN\s+)?`?(\w+)`?/gi;
    while ((match = dropColumnRegex.exec(content)) !== null) {
      const tableName = match[1];
      const columnName = match[2];

      if (!tableName || !columnName) continue;

      const table = this.tables.get(tableName);
      if (table) {
        table.columns = table.columns.filter(c => c.columnName !== columnName);
        console.log(`[ALTER DROP] ${tableName}.${columnName} 컬럼 삭제`);
      }
    }
  }

  /**
   * DROP TABLE 문 처리
   */
  private async processDropTables(content: string): Promise<void> {
    const dropTableRegex = /DROP\s+TABLE\s+(?:IF\s+EXISTS\s+)?`?(\w+)`?/gi;
    let match;

    while ((match = dropTableRegex.exec(content)) !== null) {
      const tableName = match[1];
      if (tableName) {
        this.tables.delete(tableName);
        console.log(`[DROP] 테이블 삭제: ${tableName}`);
      }
    }
  }



  /**
   * 최종 스키마 반환
   */
  getFinalSchema(): DatabaseTableInfo[] {
    const tables = Array.from(this.tables.values());

    // 테이블 플래그 업데이트
    for (const table of tables) {
      this.updateTableFlagsFromColumns(table);
    }

    return tables;
  }

  /**
   * 컬럼 정보를 기반으로 테이블 플래그 업데이트
   */
  private updateTableFlagsFromColumns(table: DatabaseTableInfo): void {
    table.hasAccountId = table.columns.some(col => col.relatedTo === 'accountId');
    table.hasPubId = table.columns.some(col => col.relatedTo === 'pubId');
  }



  /**
   * 빈 테이블 정보 생성
   */
  private createEmptyTableInfo(tableName: string, filePath: string): DatabaseTableInfo {
    const database = this.determineDatabaseType(filePath);

    return {
      tableName,
      database,
      columns: [],
      primaryKeys: [],
      indexes: [],
      hasAccountId: false,
      hasPubId: false,
      shardingRequired: database === 'user',
    };
  }

  /**
   * 테이블 플래그 업데이트
   */
  private updateTableFlags(table: DatabaseTableInfo, column: DatabaseColumnInfo): void {
    if (column.relatedTo === 'accountId') {
      table.hasAccountId = true;
    } else if (column.relatedTo === 'pubId') {
      table.hasPubId = true;
    }
  }

  // 유틸리티 메서드들 (CodebaseAnalyzer에서 복사)
  private determineDatabaseType(filePath: string): 'auth' | 'world' | 'user' {
    const normalizedPath = filePath.replace(/\\/g, '/').toLowerCase();

    if (normalizedPath.includes('/auth/')) {
      return 'auth';
    } else if (normalizedPath.includes('/world/')) {
      return 'world';
    } else if (normalizedPath.includes('/user/')) {
      return 'user';
    }

    return 'user';
  }

  private isValidTableName(tableName: string, database: 'auth' | 'world' | 'user'): boolean {
    const lowerTableName = tableName.toLowerCase();

    switch (database) {
      case 'auth':
        return lowerTableName.startsWith('a_');
      case 'world':
        return lowerTableName.startsWith('w_');
      case 'user':
        return lowerTableName.startsWith('u_');
      default:
        return false;
    }
  }

  /**
   * 컬럼과 GNID/NID의 관계 결정
   */
  private determineColumnRelation(columnName: string, tableName?: string): 'accountId' | 'pubId' | 'none' {
    const lowerColumnName = columnName.toLowerCase();
    const lowerTableName = tableName?.toLowerCase() || '';

    // GNID/AccountID 관련 컬럼들
    if (lowerColumnName === 'gnid' ||
        lowerColumnName === 'accountid' ||
        lowerColumnName === 'account_id') {
      return 'accountId';
    }

    // NID/PubID 관련 컬럼들
    if (lowerColumnName === 'nid' ||
        lowerColumnName === 'pubid' ||
        lowerColumnName === 'pub_id') {
      return 'pubId';
    }

    // 특수 케이스: a_pub_ids 테이블의 accountId 컬럼
    if (lowerTableName === 'a_pub_ids' && lowerColumnName === 'accountid') {
      return 'accountId';
    }

    return 'none';
  }

  /**
   * 컬럼 정의 파싱 (fallback용)
   */
  private parseColumnDefinition(columnName: string, definition: string, tableName?: string): DatabaseColumnInfo | null {
    if (!columnName || !definition) return null;

    // 데이터 타입 추출
    const typeMatch = definition.match(/^(\w+)(?:\(([^)]+)\))?/);
    let dataType = 'UNKNOWN';
    if (typeMatch && typeMatch[1]) {
      dataType = typeMatch[1].toUpperCase();
      if (typeMatch[2]) {
        dataType += `(${typeMatch[2]})`;
      }
    }

    // NULL 허용 여부
    const isNullable = !definition.toLowerCase().includes('not null');

    // PRIMARY KEY 여부
    const isPrimaryKey = definition.toLowerCase().includes('primary key');

    return {
      columnName,
      dataType,
      isNullable,
      isPrimaryKey,
      isIndexed: false,
      relatedTo: this.determineColumnRelation(columnName, tableName)
    };
  }

  /**
   * CREATE TABLE 정의에서 테이블 정보 생성
   */
  private createTableFromDefinition(tableName: string, definition: string, filePath: string): DatabaseTableInfo | null {
    const database = this.determineDatabaseType(filePath);

    // 테이블 이름 접두사 검증
    if (!this.isValidTableName(tableName, database)) {
      console.log(`[필터링] 잘못된 테이블 이름 접두사: ${tableName} (${database} 데이터베이스)`);
      return null;
    }

    const tableInfo: DatabaseTableInfo = {
      tableName,
      database,
      columns: [],
      primaryKeys: [],
      indexes: [],
      hasAccountId: false,
      hasPubId: false,
      shardingRequired: database === 'user',
    };

    // 컬럼 정의 파싱
    const columnDefinitions = this.parseColumnDefinitions(definition);
    for (const [columnName, columnDef] of columnDefinitions) {
      const columnInfo = this.parseColumnDefinition(columnName, columnDef, tableName);
      if (columnInfo) {
        tableInfo.columns.push(columnInfo);
        this.updateTableFlags(tableInfo, columnInfo);
      }
    }

    // PRIMARY KEY 파싱
    const primaryKeys = this.parsePrimaryKeys(definition);
    tableInfo.primaryKeys = primaryKeys;

    // PRIMARY KEY 컬럼들에 isPrimaryKey 플래그 설정
    for (const column of tableInfo.columns) {
      if (primaryKeys.includes(column.columnName)) {
        column.isPrimaryKey = true;
      }
    }

    // 인덱스 파싱
    tableInfo.indexes = this.parseIndexes(definition);

    return tableInfo;
  }

  /**
   * 컬럼 정의들을 파싱
   */
  private parseColumnDefinitions(definition: string): Map<string, string> {
    const columns = new Map<string, string>();

    // 먼저 정의를 정리하고 쉼표로 분리
    const cleanDefinition = definition
      .replace(/\s+/g, ' ')  // 여러 공백을 하나로
      .replace(/\n/g, ' ')   // 줄바꿈을 공백으로
      .trim();

    // 쉼표로 분리하되, 괄호 안의 쉼표는 무시
    const parts = this.splitByCommaIgnoringParentheses(cleanDefinition);

    for (const part of parts) {
      const trimmed = part.trim();

      // 스키마 정의가 아닌 라인들 제외
      if (!trimmed ||
          trimmed.toUpperCase().startsWith('PRIMARY KEY') ||
          trimmed.toUpperCase().startsWith('KEY ') ||
          trimmed.toUpperCase().startsWith('INDEX ') ||
          trimmed.toUpperCase().startsWith('UNIQUE KEY') ||
          trimmed.toUpperCase().startsWith('UNIQUE INDEX') ||
          trimmed.toUpperCase().startsWith('CONSTRAINT ') ||
          trimmed.toUpperCase().startsWith('FOREIGN KEY') ||
          trimmed.toUpperCase().startsWith('CHECK ')) {
        continue;
      }

      // 컬럼 정의 파싱: `columnName` TYPE ...
      const columnMatch = trimmed.match(/^`?(\w+)`?\s+(.+)$/);
      if (columnMatch && columnMatch[1] && columnMatch[2]) {
        const columnName = columnMatch[1];
        const columnDef = columnMatch[2].trim();

        // 컬럼명이 SQL 키워드가 아닌지 확인
        const sqlKeywords = ['INDEX', 'KEY', 'PRIMARY', 'UNIQUE', 'FOREIGN', 'CONSTRAINT', 'CHECK'];
        if (columnName && columnDef && !sqlKeywords.includes(columnName.toUpperCase())) {
          columns.set(columnName, columnDef);
        }
      }
    }

    return columns;
  }

  /**
   * 괄호 안의 쉼표를 무시하고 쉼표로 분리
   */
  private splitByCommaIgnoringParentheses(text: string): string[] {
    const parts: string[] = [];
    let current = '';
    let parenthesesLevel = 0;

    for (let i = 0; i < text.length; i++) {
      const char = text[i];

      if (char === '(') {
        parenthesesLevel++;
      } else if (char === ')') {
        parenthesesLevel--;
      } else if (char === ',' && parenthesesLevel === 0) {
        if (current.trim()) {
          parts.push(current.trim());
        }
        current = '';
        continue;
      }

      current += char;
    }

    if (current.trim()) {
      parts.push(current.trim());
    }

    return parts;
  }

  /**
   * PRIMARY KEY 파싱
   */
  private parsePrimaryKeys(definition: string): string[] {
    const primaryKeyMatch = definition.match(/PRIMARY\s+KEY\s*\(([^)]+)\)/i);
    if (primaryKeyMatch && primaryKeyMatch[1]) {
      return primaryKeyMatch[1]
        .split(',')
        .map(col => col.trim().replace(/`/g, ''))
        .filter(col => col.length > 0);
    }
    return [];
  }

  /**
   * 인덱스 파싱
   */
  private parseIndexes(definition: string): DatabaseIndexInfo[] {
    const indexes: DatabaseIndexInfo[] = [];
    const lines = definition.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();

      // KEY `indexName` (`column1`, `column2`)
      const keyMatch = trimmed.match(/(?:KEY|INDEX)\s+`?(\w+)`?\s*\(([^)]+)\)/i);
      if (keyMatch && keyMatch[1] && keyMatch[2]) {
        const indexName = keyMatch[1];
        const columns = keyMatch[2]
          .split(',')
          .map(col => col.trim().replace(/`/g, ''))
          .filter(col => col.length > 0);

        indexes.push({
          indexName,
          columns,
          isUnique: trimmed.toLowerCase().includes('unique'),
          isPrimary: false
        });
      }
    }

    return indexes;
  }




}

/**
 * 코드베이스 분석기
 */
export class CodebaseAnalyzer {
  private migrationsDir: string;
  private proceduresDir: string;
  private redisScriptDir: string;
  private srcDir: string;

  // private readonly EXCLUDED_TABLES = new Set([
  //   'a_users',
  //   'a_pub_id_mappings', // 마이그레이션에서 실제로 삭제됨
  //   'u_clash_last_fought_sessions',
  //   'u_clashs',
  //   'u_infinite_light_house',
  //   'u_town_remote_invest',
  //   'u_raid_tickets'
  // ]);

  // 마이그레이션에서 삭제된 테이블들을 추적
  private droppedTables = new Set<string>();

  constructor(baseDir: string = '../node') {
    // this.baseDir = baseDir;
    this.migrationsDir = path.join(baseDir, 'migrations');
    this.proceduresDir = path.join(baseDir, 'procedures');
    this.redisScriptDir = path.join(baseDir, 'redis_script');
    this.srcDir = path.join(baseDir, 'src');
  }

  /**
   * 전체 코드베이스 분석
   */
  async analyzeCodebase(schemaDir?: string): Promise<AnalysisResult> {
    const startTime = Date.now();

    console.log('코드베이스 분석을 시작합니다...');

    // 병렬로 분석 실행
    const [databaseTables, redisKeys, fileCounts] = await Promise.all([
      schemaDir ? this.loadSchemaFromFiles(schemaDir) : this.analyzeDatabaseStructure(),
      this.analyzeRedisKeys(),
      this.countAnalyzedFiles(),
    ]);

    const analysisTime = Date.now() - startTime;

    return {
      databaseTables,
      redisKeys,
      totalFilesAnalyzed: fileCounts.total,
      sqlFilesAnalyzed: fileCounts.sql,
      luaFilesAnalyzed: fileCounts.lua,
      tsFilesAnalyzed: fileCounts.ts,
      analysisTime,
    };
  }

  /**
   * 스키마 파일에서 테이블 정보 로드
   */
  private async loadSchemaFromFiles(schemaDir: string): Promise<DatabaseTableInfo[]> {
    console.log(`📁 스키마 파일에서 테이블 정보 로드: ${schemaDir}`);

    const schemaFiles = await glob('schema-*.json5', { cwd: schemaDir });
    console.log(`발견된 스키마 파일: ${schemaFiles.length}개`);

    const allTables: DatabaseTableInfo[] = [];

    for (const fileName of schemaFiles) {
      const filePath = path.join(schemaDir, fileName);
      try {
        const schemaContent = await fs.readFile(filePath, 'utf8');
        const schemaData = JSON5.parse(schemaContent);
        console.log(`  📋 ${fileName}: ${schemaData.tables.length}개 테이블`);

        // 스키마 데이터를 DatabaseTableInfo 형식으로 변환
        const tables = this.convertSchemaToTableInfo(schemaData.tables, schemaData.database);
        allTables.push(...tables);
      } catch (error) {
        console.error(`❌ 스키마 파일 로드 실패: ${fileName}`, error);
      }
    }

    // 필터링된 테이블만 반환
    const filteredTables = this.filterRelevantTables(allTables);

    console.log(`총 ${allTables.length}개 테이블 로드, ${filteredTables.length}개 테이블이 GNID/NID 관련`);

    return filteredTables;
  }

  /**
   * 스키마 데이터를 DatabaseTableInfo로 변환
   */
  private convertSchemaToTableInfo(tables: any[], database: string): DatabaseTableInfo[] {
    return tables
      .filter(table => {
        // 테이블 이름 접두사 검증
        if (!this.isValidTableName(table.tableName, table.database)) {
          console.log(`[필터링] 잘못된 테이블 이름 접두사: ${table.tableName} (${table.database} 데이터베이스)`);
          return false;
        }
        return true;
      })
      .map(table => ({
        tableName: table.tableName,
        database: table.database,
        columns: table.columns,
        primaryKeys: table.primaryKeys,
        indexes: table.indexes,
        hasAccountId: table.hasAccountId,
        hasPubId: table.hasPubId,
        shardingRequired: this.determineShardingRequired(table),
        filePath: table.filePath,
        comment: table.comment,
        engine: table.engine,
        collation: table.collation
      }));
  }

  /**
   * 샤딩 필요 여부 결정
   */
  private determineShardingRequired(table: any): boolean {
    // user 데이터베이스의 테이블은 샤딩이 필요
    return table.database === 'user';
  }

  /**
   * 데이터베이스 구조 분석 (SQL 파일 기반)
   */
  private async analyzeDatabaseStructure(): Promise<DatabaseTableInfo[]> {
    console.log('📁 SQL 파일에서 데이터베이스 구조 분석 중...');

    try {
      // 여러 위치에서 SQL 파일들 찾기
      const searchPaths = [
        // this.srcDir,
        // path.join(this.srcDir, '..', 'sql'),
        // path.join(this.srcDir, '..', 'database'),
        // path.join(this.srcDir, '..', 'migrations'),
        // path.join(this.srcDir, '..', 'schema'),
        this.migrationsDir,
        this.proceduresDir
      ];

      const allSqlFiles: string[] = [];
      for (const searchPath of searchPaths) {
        try {
          const files = await this.findSqlFilesRecursive(searchPath);
          allSqlFiles.push(...files);
        } catch (error) {
          // 디렉토리가 없으면 무시
        }
      }

      const uniqueSqlFiles = [...new Set(allSqlFiles)];
      console.log(`발견된 SQL 파일: ${uniqueSqlFiles.length}개`);

      if (uniqueSqlFiles.length === 0) {
        console.log('⚠️ SQL 파일을 찾을 수 없습니다.');
        console.log('💡 extract-schema 명령어를 사용하여 실제 데이터베이스에서 스키마를 추출하는 것을 권장합니다.');
        return [];
      }

      const allTables: DatabaseTableInfo[] = [];

      for (const filePath of uniqueSqlFiles) {
        try {
          const tables = await this.analyzeSqlFile(filePath);
          allTables.push(...tables);
        } catch (error) {
          console.warn(`SQL 파일 분석 실패: ${filePath}`, error);
        }
      }

      // 중복 제거 및 병합
      const mergedTables = this.mergeDatabaseTableInfos(allTables);

      // 필터링된 테이블만 반환
      const filteredTables = this.filterRelevantTables(mergedTables);

      console.log(`총 ${allTables.length}개 테이블 발견, ${filteredTables.length}개 테이블이 GNID/NID 관련`);

      if (filteredTables.length === 0) {
        console.log('⚠️ GNID/NID 관련 테이블을 찾을 수 없습니다.');
        console.log('💡 extract-schema 명령어를 사용하여 실제 데이터베이스에서 스키마를 추출하는 것을 권장합니다.');
      }

      return filteredTables;
    } catch (error) {
      console.error('❌ 데이터베이스 구조 분석 실패:', error);
      console.log('💡 extract-schema 명령어를 사용하여 실제 데이터베이스에서 스키마를 추출하세요.');
      return [];
    }
  }

  /**
   * 디렉토리에서 SQL 파일들 재귀적으로 찾기
   */
  private async findSqlFilesRecursive(dir: string): Promise<string[]> {
    const sqlFiles: string[] = [];

    try {
      if (!await fs.pathExists(dir)) {
        return [];
      }

      const entries = await fs.readdir(dir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);

        if (entry.isDirectory()) {
          const subFiles = await this.findSqlFilesRecursive(fullPath);
          sqlFiles.push(...subFiles);
        } else if (entry.isFile() && entry.name.toLowerCase().endsWith('.sql')) {
          sqlFiles.push(fullPath);
        }
      }
    } catch (error) {
      // 디렉토리 접근 오류는 무시
    }

    return sqlFiles;
  }

  /**
   * Redis 키 분석
   */
  private async analyzeRedisKeys(): Promise<RedisKeyInfo[]> {
    // 정의된 키 패턴을 기본으로 사용하되, 실제 코드 분석도 수행
    const definedPatterns = getAllRedisKeyPatterns();
    console.log(`정의된 Redis 키 패턴 ${definedPatterns.length}개 사용`);

    // 실제 코드 분석도 수행 (Lua 및 TypeScript 파일)
    const [luaKeys, tsKeys] = await Promise.all([
      this.analyzeRedisKeysFromLua(),
      this.analyzeRedisKeysFromTypeScript(),
    ]);

    // 정의된 패턴과 코드 분석 결과를 병합
    const allKeys = [...definedPatterns, ...luaKeys, ...tsKeys];

    // 중복 제거 (keyPattern 기준)
    const uniqueKeys = this.deduplicateRedisKeys(allKeys);

    console.log(`총 ${uniqueKeys.length}개 Redis 키 패턴 발견 (정의된 패턴: ${definedPatterns.length}, Lua: ${luaKeys.length}, TypeScript: ${tsKeys.length})`);

    // 각 패턴에 대해 타입 추론 추가 (이미 keyType이 있으면 유지)
    return uniqueKeys.map(pattern => ({
      ...pattern,
      keyType: pattern.keyType || this.inferKeyTypeFromCode(pattern.keyPattern),
      keyCount: pattern.keyCount || 0
    }));
  }

  /**
   * Lua 파일들에서 Redis 키 분석
   */
  private async analyzeRedisKeysFromLua(): Promise<RedisKeyInfo[]> {
    const luaFiles = await this.findLuaFiles(this.redisScriptDir);
    const allKeys: RedisKeyInfo[] = [];

    for (const filePath of luaFiles) {
      try {
        const keys = await this.analyzeLuaFile(filePath);
        allKeys.push(...keys);
      } catch (error) {
        console.warn(`Lua 파일 분석 실패: ${filePath}`, error);
      }
    }

    return this.filterRelevantRedisKeys(allKeys);
  }

  /**
   * TypeScript 파일들에서 Redis 키 분석
   */
  private async analyzeRedisKeysFromTypeScript(): Promise<RedisKeyInfo[]> {
    const tsFiles = await this.findTsFiles(this.srcDir);
    const allKeys: RedisKeyInfo[] = [];

    for (const filePath of tsFiles) {
      try {
        const keys = await this.analyzeTsFile(filePath);
        allKeys.push(...keys);
      } catch (error) {
        console.warn(`TypeScript 파일 분석 실패: ${filePath}`, error);
      }
    }

    return this.filterRelevantRedisKeys(allKeys);
  }



  /**
   * 코드 분석을 통한 키 타입 추론
   */
  private inferKeyTypeFromCode(keyPattern: string): string {
    // 1. 정적 패턴 분석
    const staticType = this.getStaticKeyType(keyPattern);
    if (staticType !== 'unknown') {
      return staticType;
    }

    // 2. 코드베이스에서 Redis 명령어 패턴 분석
    return this.analyzeRedisCommandPatterns(keyPattern);
  }

  /**
   * 정적 키 타입 매핑 (코드 분석 결과 기반)
   */
  private getStaticKeyType(keyPattern: string): string {
    const staticTypes: Record<string, string> = {
      // Hash 타입 - HSET, HGET, HMSET, HMGET 명령어 사용
      'account:{accountId}': 'hash',        // HMSET, HMGET, HSET 사용
      'user:{userId}': 'hash',              // HSET, HGETALL 사용
      'guildMember:{guildId}': 'hash',
      'pickedDailyRewardIdx:{guildId}': 'hash',
      'nationCabinet:{nationCmsId}:{sessionId}': 'hash',
      'userIdsByName': 'hash',
      'inProgress': 'hash',

      // Sorted Set 타입 - ZADD, ZREM, ZSCORE, ZCARD 명령어 사용
      'prologueGnids:{worldId}': 'zset',    // ZADD, ZREM, ZSCORE, ZCARD 사용

      // List 타입 - LPUSH, RPUSH, LRANGE, LREM 명령어 사용
      'deletionPubIds': 'list',             // RPUSH, LRANGE, LREM 사용

      // String 타입 - SET, GET 명령어 사용
      'townUserWeeklyInvestmentReport:{nid}': 'string', // SET 사용
      'eu:{userId}': 'string'               // SET 사용
    };

    return staticTypes[keyPattern] || 'unknown';
  }

  /**
   * Redis 명령어 패턴 분석을 통한 타입 추론
   */
  private analyzeRedisCommandPatterns(keyPattern: string): string {
    // 키 패턴에서 기본 키 이름 추출
    const baseKeyName = this.extractBaseKeyName(keyPattern);

    // 코드베이스에서 해당 키와 관련된 Redis 명령어 검색
    const commands = this.findRedisCommandsForKey(baseKeyName);

    // 명령어 패턴을 기반으로 타입 추론
    return this.inferTypeFromCommands(commands);
  }





  /**
   * 키 패턴에서 기본 키 이름 추출
   */
  private extractBaseKeyName(keyPattern: string): string {
    // {accountId}, {userId} 등의 플레이스홀더 제거
    return keyPattern
      .replace(/\{[^}]+\}/g, '*')
      .replace(/:\*$/, '');
  }

  /**
   * 키와 관련된 Redis 명령어 찾기 (정적 분석)
   */
  private findRedisCommandsForKey(baseKeyName: string): string[] {
    // 실제 구현에서는 Lua 스크립트와 TypeScript 파일을 분석
    // 여기서는 코드베이스 분석 결과를 기반으로 매핑
    const keyCommandMap: Record<string, string[]> = {
      'account': ['HMSET', 'HMGET', 'HSET'],
      'user': ['HSET', 'HGETALL', 'HGET'],
      'prologueGnids': ['ZADD', 'ZREM', 'ZSCORE', 'ZCARD', 'ZREMRANGEBYSCORE'],
      'deletionPubIds': ['RPUSH', 'LRANGE', 'LREM'],
      'townUserWeeklyInvestmentReport': ['SET'],
      'eu': ['SET', 'GET']
    };

    for (const [key, commands] of Object.entries(keyCommandMap)) {
      if (baseKeyName.includes(key)) {
        return commands;
      }
    }

    return [];
  }

  /**
   * Redis 명령어를 기반으로 타입 추론
   */
  private inferTypeFromCommands(commands: string[]): string {
    if (commands.length === 0) return 'unknown';

    // Hash 명령어들
    const hashCommands = ['HSET', 'HGET', 'HMSET', 'HMGET', 'HGETALL', 'HDEL', 'HEXISTS'];
    if (commands.some(cmd => hashCommands.includes(cmd))) {
      return 'hash';
    }

    // Sorted Set 명령어들
    const zsetCommands = ['ZADD', 'ZREM', 'ZSCORE', 'ZCARD', 'ZRANGE', 'ZREVRANGE', 'ZREMRANGEBYSCORE'];
    if (commands.some(cmd => zsetCommands.includes(cmd))) {
      return 'zset';
    }

    // List 명령어들
    const listCommands = ['LPUSH', 'RPUSH', 'LPOP', 'RPOP', 'LRANGE', 'LREM', 'LLEN'];
    if (commands.some(cmd => listCommands.includes(cmd))) {
      return 'list';
    }

    // Set 명령어들
    const setCommands = ['SADD', 'SREM', 'SMEMBERS', 'SISMEMBER', 'SCARD'];
    if (commands.some(cmd => setCommands.includes(cmd))) {
      return 'set';
    }

    // String 명령어들
    const stringCommands = ['SET', 'GET', 'SETEX', 'SETNX', 'INCR', 'DECR'];
    if (commands.some(cmd => stringCommands.includes(cmd))) {
      return 'string';
    }

    return 'unknown';
  }

  /**
   * SQL 파일 분석
   */
  private async analyzeSqlFile(filePath: string): Promise<DatabaseTableInfo[]> {
    const content = await fs.readFile(filePath, 'utf-8');
    const tables: DatabaseTableInfo[] = [];
    
    // CREATE TABLE 문 분석
    const createTableRegex = /CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?\s*\(([\s\S]*?)\)/gi;
    let match;
    
    while ((match = createTableRegex.exec(content)) !== null) {
      const tableName = match[1] || '';
      const tableDefinition = match[2] || '';

      // prefix 검증 - 올바른 prefix를 가진 테이블만 분석
      const database = this.determineDatabaseType(filePath);
      if (!this.isValidTableName(tableName, database)) {
        continue; // 잘못된 prefix를 가진 테이블은 건너뛰기
      }

      const tableInfo = this.parseTableDefinition(tableName, tableDefinition, filePath);
      if (tableInfo) {
        tables.push(tableInfo);
      }
    }

    // ALTER TABLE 문에서 컬럼 정보 추출
    const alterTableRegex = /ALTER\s+TABLE\s+`?(\w+)`?\s+(ADD|MODIFY|CHANGE)\s+(?:COLUMN\s+)?`?(\w+)`?\s+([^,;]+)/gi;
    while ((match = alterTableRegex.exec(content)) !== null) {
      const tableName = match[1] || '';
      const columnName = match[3] || '';
      const columnDef = match[4] || '';

      // prefix 검증 - 올바른 prefix를 가진 테이블만 분석
      const database = this.determineDatabaseType(filePath);
      if (!this.isValidTableName(tableName, database)) {
        continue; // 잘못된 prefix를 가진 테이블은 건너뛰기
      }

      // 기존 테이블 정보 업데이트 또는 새로 생성
      let existingTable = tables.find(t => t.tableName === tableName);
      if (!existingTable) {
        existingTable = this.createEmptyTableInfo(tableName, filePath || '');
        tables.push(existingTable);
      }

      const columnInfo = this.parseColumnDefinition(columnName, columnDef, tableName);
      if (columnInfo && !existingTable.columns.find(c => c.columnName === columnName)) {
        existingTable.columns.push(columnInfo);
        this.updateTableFlags(existingTable, columnInfo);
      }
    }

    return tables;
  }

  /**
   * 테이블 정의 파싱
   */
  public parseTableDefinition(tableName: string, definition: string, filePath: string): DatabaseTableInfo | null {
    const columns: DatabaseColumnInfo[] = [];
    const indexes: DatabaseIndexInfo[] = [];
    const primaryKeys: string[] = [];

    // 컬럼 정의 파싱
    const lines = definition.split('\n').map(line => line.trim());
    
    for (const line of lines) {
      // 컬럼 정의 (백틱 있거나 없거나 모두 처리)
      const columnMatch = line.match(/^\s*(?:`(\w+)`|(\w+))\s+(.+?)(?:,\s*$|$)/);
      if (columnMatch && !line.match(/^\s*(PRIMARY|UNIQUE|INDEX|KEY|CONSTRAINT|ENGINE|DEFAULT|CHARSET)/i)) {
        const columnName = columnMatch[1] || columnMatch[2] || '';
        const columnDef = columnMatch[3] || '';
        const columnInfo = this.parseColumnDefinition(columnName, columnDef, tableName);
        if (columnInfo) {
          columns.push(columnInfo);
        }
      } else if (line.startsWith('PRIMARY KEY')) {
        // 기본 키 정의
        const pkMatch = line.match(/PRIMARY\s+KEY\s*\(([^)]+)\)/i);
        if (pkMatch && pkMatch[1]) {
          const pkColumns = pkMatch[1].split(',').map(col => col.trim().replace(/`/g, ''));
          primaryKeys.push(...pkColumns);
        }
      } else if (line.includes('INDEX') || line.includes('KEY')) {
        // 인덱스 정의
        const indexInfo = this.parseIndexDefinition(line);
        if (indexInfo) {
          indexes.push(indexInfo);
        }
      }
    }

    // 기본 키 정보를 컬럼에 반영
    for (const column of columns) {
      if (primaryKeys.includes(column.columnName)) {
        column.isPrimaryKey = true;
      }
    }

    const database = this.determineDatabaseType(filePath);
    const tableInfo: DatabaseTableInfo = {
      tableName,
      database,
      columns,
      primaryKeys,
      indexes,
      hasAccountId: false,
      hasPubId: false,
      shardingRequired: database === 'user',
    };

    // 플래그 업데이트
    for (const column of columns) {
      this.updateTableFlags(tableInfo, column);
    }

    return tableInfo;
  }

  /**
   * 컬럼 정의 파싱
   */
  public parseColumnDefinition(columnName: string, definition: string, tableName?: string): DatabaseColumnInfo | null {
    const lowerDef = definition.toLowerCase();
    const lowerName = columnName.toLowerCase();

    // 데이터 타입 추출 (더 정확한 정규식)
    const dataType = this.extractDataType(definition);

    // NULL 허용 여부
    const isNullable = !lowerDef.includes('not null');

    // 인덱스 여부 (컬럼 레벨에서만, 테이블 레벨 인덱스는 제외)
    // 컬럼 정의에서 직접 INDEX나 KEY가 명시된 경우만
    const isIndexed = this.isColumnLevelIndexed(definition);

    let relatedTo: 'accountId' | 'pubId' | 'none' = 'none';

    // 정확한 필드명만 매칭 (더 엄격한 조건)
    if (lowerName === 'accountid' || lowerName === 'gnid') {
      relatedTo = 'accountId';
    } else if (lowerName === 'pubid' || lowerName === 'nid') {
      relatedTo = 'pubId';
    }

    // 특수 케이스: a_accounts 테이블의 id 필드는 accountId
    if (tableName && tableName.toLowerCase() === 'a_accounts' && lowerName === 'id') {
      relatedTo = 'accountId';
    }

    return {
      columnName,
      dataType,
      isNullable,
      isPrimaryKey: false, // 나중에 설정됨
      isIndexed,
      relatedTo,
    };
  }

  /**
   * 데이터 타입 추출 (개선된 버전)
   */
  private extractDataType(definition: string): string {
    // 공백으로 분리하여 첫 번째 토큰이 데이터 타입
    const tokens = definition.trim().split(/\s+/);
    if (tokens.length === 0) return 'UNKNOWN';

    const firstToken = tokens[0];
    if (!firstToken) return 'UNKNOWN';

    // MySQL 데이터 타입 패턴 매칭
    const typePatterns = [
      // 숫자 타입
      /^(TINYINT|SMALLINT|MEDIUMINT|INT|INTEGER|BIGINT)(\(\d+\))?(\s+UNSIGNED)?/i,
      /^(DECIMAL|NUMERIC|FLOAT|DOUBLE|REAL)(\(\d+(,\d+)?\))?(\s+UNSIGNED)?/i,
      /^(BIT)(\(\d+\))?/i,

      // 문자열 타입
      /^(CHAR|VARCHAR)(\(\d+\))?/i,
      /^(BINARY|VARBINARY)(\(\d+\))?/i,
      /^(TINYBLOB|BLOB|MEDIUMBLOB|LONGBLOB)/i,
      /^(TINYTEXT|TEXT|MEDIUMTEXT|LONGTEXT)/i,

      // 날짜/시간 타입
      /^(DATE|TIME|DATETIME|TIMESTAMP|YEAR)(\(\d+\))?/i,

      // JSON 타입
      /^(JSON)/i,

      // ENUM, SET
      /^(ENUM|SET)\(.+\)/i,
    ];

    for (const pattern of typePatterns) {
      const match = firstToken.match(pattern);
      if (match) {
        return match[0].toUpperCase();
      }
    }

    // 기본 타입 체크 (괄호 포함)
    const basicMatch = firstToken.match(/^(\w+)(\([^)]+\))?/i);
    if (basicMatch && basicMatch[1]) {
      const baseType = basicMatch[1].toUpperCase();
      const params = basicMatch[2] || '';

      // 알려진 MySQL 타입인지 확인
      const knownTypes = [
        'TINYINT', 'SMALLINT', 'MEDIUMINT', 'INT', 'INTEGER', 'BIGINT',
        'DECIMAL', 'NUMERIC', 'FLOAT', 'DOUBLE', 'REAL', 'BIT',
        'CHAR', 'VARCHAR', 'BINARY', 'VARBINARY',
        'TINYBLOB', 'BLOB', 'MEDIUMBLOB', 'LONGBLOB',
        'TINYTEXT', 'TEXT', 'MEDIUMTEXT', 'LONGTEXT',
        'DATE', 'TIME', 'DATETIME', 'TIMESTAMP', 'YEAR',
        'JSON', 'ENUM', 'SET'
      ];

      if (knownTypes.includes(baseType)) {
        return baseType + params;
      }
    }

    return 'UNKNOWN';
  }

  /**
   * 컬럼 레벨 인덱스 여부 확인
   */
  private isColumnLevelIndexed(_definition: string): boolean {
    // 컬럼 정의에서 직접 INDEX나 KEY가 명시된 경우만
    // 하지만 대부분의 인덱스는 테이블 레벨에서 정의되므로 false 반환
    // 이렇게 하면 "INDEX" 컬럼이 나타나지 않음
    return false;
  }

  /**
   * 인덱스 정의 파싱
   */
  private parseIndexDefinition(line: string): DatabaseIndexInfo | null {
    // INDEX, KEY, UNIQUE INDEX 등을 파싱
    const indexMatch = line.match(/(UNIQUE\s+)?(INDEX|KEY)\s+(?:`?(\w+)`?\s+)?\(([^)]+)\)/i);
    if (!indexMatch) return null;

    const isUnique = !!indexMatch[1];
    const indexName = indexMatch[3] || 'unnamed';
    const columnsStr = indexMatch[4] || '';
    
    const columns = columnsStr.split(',').map(col => col.trim().replace(/`/g, ''));

    return {
      indexName,
      columns,
      isUnique,
      isPrimary: false,
    };
  }

  /**
   * 데이터베이스 타입 결정
   */
  private determineDatabaseType(filePath: string): 'auth' | 'world' | 'user' {
    const normalizedPath = filePath.replace(/\\/g, '/').toLowerCase();
    
    if (normalizedPath.includes('/auth/')) {
      return 'auth';
    } else if (normalizedPath.includes('/world/')) {
      return 'world';
    } else if (normalizedPath.includes('/user/')) {
      return 'user';
    }
    
    // 기본값은 user (가장 많은 테이블이 있음)
    return 'user';
  }

  /**
   * 테이블 플래그 업데이트
   */
  private updateTableFlags(tableInfo: DatabaseTableInfo, column: DatabaseColumnInfo): void {
    switch (column.relatedTo) {
      case 'accountId':
        tableInfo.hasAccountId = true;
        break;
      case 'pubId':
        tableInfo.hasPubId = true;
        break;
    }
  }

  /**
   * 빈 테이블 정보 생성
   */
  private createEmptyTableInfo(tableName: string, filePath: string): DatabaseTableInfo {
    return {
      tableName,
      database: this.determineDatabaseType(filePath),
      columns: [],
      primaryKeys: [],
      indexes: [],
      hasAccountId: false,
      hasPubId: false,
      shardingRequired: this.determineDatabaseType(filePath) === 'user',
    };
  }

  /**
   * 마이그레이션 파일을 시간순으로 정렬하여 찾기
   */
  private async findMigrationFilesInOrder(directory: string): Promise<string[]> {
    if (!await fs.pathExists(directory)) {
      return [];
    }

    const files = await glob('**/*.sql', { cwd: directory, absolute: true });

    // 마이그레이션 파일명에서 타임스탬프 추출하여 정렬
    return files.sort((a, b) => {
      const timestampA = this.extractTimestampFromMigrationFile(a);
      const timestampB = this.extractTimestampFromMigrationFile(b);
      return timestampA.localeCompare(timestampB);
    });
  }

  /**
   * 마이그레이션 파일명에서 타임스탬프 추출
   */
  private extractTimestampFromMigrationFile(filePath: string): string {
    const fileName = path.basename(filePath);
    // 일반적인 마이그레이션 파일명 패턴: YYYYMMDDHHMMSS-description.sql
    const match = fileName.match(/^(\d{14})/);
    return match && match[1] ? match[1] : '00000000000000';
  }

  /**
   * SQL 파일 찾기
   */
  private async findSqlFiles(directory: string): Promise<string[]> {
    if (!await fs.pathExists(directory)) {
      return [];
    }

    return glob('**/*.sql', { cwd: directory, absolute: true });
  }

  /**
   * Lua 파일 찾기
   */
  private async findLuaFiles(directory: string): Promise<string[]> {
    if (!await fs.pathExists(directory)) {
      return [];
    }

    return glob('**/*.lua', { cwd: directory, absolute: true });
  }

  /**
   * TypeScript 파일 찾기
   */
  private async findTsFiles(directory: string): Promise<string[]> {
    if (!await fs.pathExists(directory)) {
      return [];
    }

    return glob('**/*.ts', { cwd: directory, absolute: true });
  }

  /**
   * GNID/NID 리맵핑과 관련된 테이블만 필터링
   */
  /**
   * 마이그레이션 히스토리 분석 (삭제된 테이블 추적)
   *
   * 주의: 이 메서드는 실제로 사용하지 않습니다.
   * 마이그레이션 파일에서 DROP TABLE 문을 찾는 것은 신뢰할 수 없는 방법입니다.
   * 왜냐하면 down 마이그레이션에서도 DROP TABLE 문이 나타나기 때문입니다.
   */
  private async analyzeMigrationHistory(): Promise<void> {
    console.log('마이그레이션 히스토리 분석은 사용하지 않습니다.');
    // 이 메서드는 실제로 아무 작업도 수행하지 않습니다.
    // 삭제된 테이블 집합을 비웁니다.
    this.droppedTables.clear();
  }

  private filterRelevantTables(tables: DatabaseTableInfo[]): DatabaseTableInfo[] {
    return tables.filter(table => {
      // // 블랙리스트 체크
      // if (this.EXCLUDED_TABLES.has(table.tableName)) {
      //   console.log(`[필터링] 제외된 테이블: ${table.tableName} (블랙리스트)`);
      //   return false;
      // }

      // 1. accountId나 pubId 컬럼이 있는 테이블
      if (table.hasAccountId || table.hasPubId) {
        return true;
      }

      // 2. 중요한 테이블들은 컬럼이 없어도 포함 (리맵핑 시 영향받을 수 있음)
      const importantTables = [
        'u_users',           // 사용자 기본 정보
        'w_auto_sailings',   // 자동 항해
        'a_accounts',        // 계정 정보
        'a_world_users',     // 월드 사용자
        'a_pub_ids'          // PubID 매핑
      ];

      if (importantTables.includes(table.tableName)) {
        console.log(`[필터링] 중요 테이블 포함: ${table.tableName} (hasAccountId: ${table.hasAccountId}, hasPubId: ${table.hasPubId})`);
        return true;
      }

      // console.log(`[필터링] 제외된 테이블: ${table.tableName} (GNID/NID 관련 컬럼 없음)`);
      return false;
    });
  }

  /**
   * 테이블 이름 prefix 검증
   */
  private isValidTableName(tableName: string, database: 'auth' | 'world' | 'user'): boolean {
    const lowerTableName = tableName.toLowerCase();

    switch (database) {
      case 'auth':
        return lowerTableName.startsWith('a_');
      case 'world':
        return lowerTableName.startsWith('w_');
      case 'user':
        return lowerTableName.startsWith('u_');
      default:
        return false;
    }
  }

  /**
   * a_pub_ids 테이블의 누락된 컬럼들을 수정
   */
  public fixAPubIdsTable(table: DatabaseTableInfo): void {
    console.log(`[특수 처리] a_pub_ids 테이블 컬럼 수정`);

    // pubId 컬럼이 없으면 추가
    const hasPubId = table.columns.some(col => col.columnName.toLowerCase() === 'pubid');
    if (!hasPubId) {
      table.columns.push({
        columnName: 'pubId',
        dataType: 'VARCHAR(32)',
        isNullable: false,
        isPrimaryKey: true,
        isIndexed: false, // PRIMARY KEY는 자동으로 인덱스이므로 별도 인덱스 플래그는 false
        relatedTo: 'pubId'
      });
      console.log(`[특수 처리] pubId 컬럼 추가됨`);
    }

    // worldId 컬럼이 없으면 추가
    const hasWorldId = table.columns.some(col => col.columnName.toLowerCase() === 'worldid');
    if (!hasWorldId) {
      table.columns.push({
        columnName: 'worldId',
        dataType: 'VARCHAR(32)',
        isNullable: false,
        isPrimaryKey: false,
        isIndexed: false, // 테이블 레벨 인덱스는 별도 처리
        relatedTo: 'none'
      });
      console.log(`[특수 처리] worldId 컬럼 추가됨`);
    }

    // 기존 컬럼들의 isIndexed 플래그 수정 (PRIMARY KEY는 별도 처리)
    for (const column of table.columns) {
      if (column.isPrimaryKey) {
        column.isIndexed = false; // PRIMARY KEY는 별도 처리
      }
    }

    // 플래그 업데이트
    table.hasAccountId = table.columns.some(col => col.relatedTo === 'accountId');
    table.hasPubId = table.columns.some(col => col.relatedTo === 'pubId');

    console.log(`[특수 처리] a_pub_ids 테이블 플래그: hasAccountId=${table.hasAccountId}, hasPubId=${table.hasPubId}`);
  }

  /**
   * GNID/NID 리맵핑과 관련된 Redis 키만 필터링
   */
  private filterRelevantRedisKeys(keys: RedisKeyInfo[]): RedisKeyInfo[] {
    return keys.filter(key => {
      // accountId(GNID), pubId(NID), gnid, nid만 사용하는 키만 포함 (userId 제외)
      const isRelevant = key.usesAccountId || key.usesPubId ||
                        key.usesGnid || key.usesNid;

      if (!isRelevant) {
        console.log(`[필터링] 제외된 Redis 키: ${key.keyPattern} (GNID/NID 관련 없음)`);
      }

      return isRelevant;
    });
  }

  /**
   * 분석된 파일 수 계산
   */
  private async countAnalyzedFiles(): Promise<{ total: number; sql: number; lua: number; ts: number }> {
    const [sqlFiles, luaFiles, tsFiles] = await Promise.all([
      this.findSqlFiles(this.migrationsDir).then(files1 => 
        this.findSqlFiles(this.proceduresDir).then(files2 => [...files1, ...files2])
      ),
      this.findLuaFiles(this.redisScriptDir),
      this.findTsFiles(this.srcDir),
    ]);

    return {
      sql: sqlFiles.length,
      lua: luaFiles.length,
      ts: tsFiles.length,
      total: sqlFiles.length + luaFiles.length + tsFiles.length,
    };
  }

  /**
   * 데이터베이스 테이블 정보 병합
   */
  private mergeDatabaseTableInfos(tables: DatabaseTableInfo[]): DatabaseTableInfo[] {
    const merged = new Map<string, DatabaseTableInfo>();

    for (const table of tables) {
      const key = `${table.database}.${table.tableName}`;
      
      if (merged.has(key)) {
        const existing = merged.get(key)!;
        // 컬럼 병합
        for (const column of table.columns) {
          if (!existing.columns.find(c => c.columnName === column.columnName)) {
            existing.columns.push(column);
          }
        }
        // 인덱스 병합
        for (const index of table.indexes) {
          if (!existing.indexes.find(i => i.indexName === index.indexName)) {
            existing.indexes.push(index);
          }
        }
        // 플래그 업데이트
        existing.hasAccountId = existing.hasAccountId || table.hasAccountId;
        existing.hasPubId = existing.hasPubId || table.hasPubId;
      } else {
        merged.set(key, { ...table });
      }
    }

    return Array.from(merged.values());
  }

  /**
   * Redis 키 중복 제거
   */
  private deduplicateRedisKeys(keys: RedisKeyInfo[]): RedisKeyInfo[] {
    const unique = new Map<string, RedisKeyInfo>();

    for (const key of keys) {
      const identifier = `${key.redisInstance}.${key.keyPattern}`;
      
      if (unique.has(identifier)) {
        const existing = unique.get(identifier)!;
        // 플래그 병합
        existing.usesAccountId = existing.usesAccountId || key.usesAccountId;
        existing.usesPubId = existing.usesPubId || key.usesPubId;
        existing.usesUserId = existing.usesUserId || key.usesUserId;
        existing.usesGnid = existing.usesGnid || key.usesGnid;
        existing.usesNid = existing.usesNid || key.usesNid;
      } else {
        unique.set(identifier, { ...key });
      }
    }

    return Array.from(unique.values());
  }

  /**
   * Lua 파일 분석 (AST 기반)
   */
  private async analyzeLuaFile(filePath: string): Promise<RedisKeyInfo[]> {
    const content = await fs.readFile(filePath, 'utf-8');

    try {
      return this.extractLuaKeysWithAST(content, filePath);
    } catch (error) {
      console.warn(`Lua AST 파싱 실패, 정규식 방식으로 대체: ${filePath}`, error);
      return this.extractLuaKeysWithRegex(content, filePath);
    }
  }

  /**
   * Lua AST를 사용한 정확한 Redis 키 패턴 추출
   */
  private extractLuaKeysWithAST(content: string, filePath: string): RedisKeyInfo[] {
    const keys: RedisKeyInfo[] = [];
    const redisInstance = this.determineRedisInstance(filePath);

    // Lua AST 파싱
    const ast = luaparse.parse(content, {
      locations: true,
      ranges: true,
      comments: false
    });

    // AST 순회하여 redis.call 찾기
    this.traverseLuaAST(ast, keys, redisInstance, path.basename(filePath));

    return keys;
  }

  /**
   * Lua AST 순회
   */
  private traverseLuaAST(node: any, keys: RedisKeyInfo[], redisInstance: string, fileName: string): void {
    if (!node || typeof node !== 'object') {
      return;
    }

    // redis.call() 호출 찾기
    if (this.isRedisCall(node)) {
      this.analyzeLuaRedisCall(node, keys, redisInstance, fileName);
    }

    // 자식 노드들 순회
    if (Array.isArray(node)) {
      node.forEach(child => this.traverseLuaAST(child, keys, redisInstance, fileName));
    } else {
      Object.values(node).forEach(child => {
        if (child && typeof child === 'object') {
          this.traverseLuaAST(child, keys, redisInstance, fileName);
        }
      });
    }
  }

  /**
   * redis.call() 호출인지 확인
   */
  private isRedisCall(node: any): boolean {
    return node.type === 'CallExpression' &&
           node.base?.type === 'MemberExpression' &&
           node.base?.base?.name === 'redis' &&
           node.base?.identifier?.name === 'call';
  }

  /**
   * Lua redis.call() 분석
   */
  private analyzeLuaRedisCall(node: any, keys: RedisKeyInfo[], redisInstance: string, fileName: string): void {
    if (!node.arguments || node.arguments.length < 2) {
      return;
    }

    const commandArg = node.arguments[0];
    const keyArg = node.arguments[1];

    // Redis 명령어 추출
    const command = this.extractLuaStringValue(commandArg);
    if (!command || !this.isRedisCommand(command)) {
      return;
    }

    // 키 패턴 추출
    const keyPattern = this.extractLuaKeyPattern(keyArg);
    if (!keyPattern || !this.isValidRedisKeyForLua(keyPattern)) {
      return;
    }

    // Redis 키 정보 생성
    const keyInfo: RedisKeyInfo = {
      keyPattern,
      redisInstance,
      usesAccountId: /(?:account|gnid)/i.test(keyPattern),
      usesPubId: /(?:nid|pubId)/i.test(keyPattern),
      usesUserId: /userId/i.test(keyPattern),
      usesGnid: /gnid/i.test(keyPattern),
      usesNid: /nid/i.test(keyPattern),
      description: `Lua 스크립트에서 발견된 ${command} 명령어 사용`,
      luaScriptPath: fileName,
      keyType: this.inferKeyTypeFromCommand(command),
      keyCount: 0
    };

    keys.push(keyInfo);
  }

  /**
   * Lua 노드에서 문자열 값 추출
   */
  private extractLuaStringValue(node: any): string | null {
    if (node.type === 'StringLiteral') {
      return node.value;
    }
    return null;
  }

  /**
   * Lua 키 패턴 추출 (문자열 연결 포함)
   */
  private extractLuaKeyPattern(node: any): string | null {
    if (node.type === 'StringLiteral') {
      return node.value;
    }

    if (node.type === 'BinaryExpression' && node.operator === '..') {
      // 문자열 연결 처리
      const left = this.extractLuaKeyPattern(node.left);
      const right = this.extractLuaKeyPattern(node.right);

      if (left && right) {
        return left + right;
      } else if (left) {
        return left + '{id}';
      } else if (right) {
        return '{id}' + right;
      }
    }

    if (node.type === 'Identifier') {
      // 변수는 플레이스홀더로 변환
      return '{id}';
    }

    return null;
  }

  /**
   * Redis 명령어인지 확인
   */
  private isRedisCommand(command: string): boolean {
    const redisCommands = [
      'GET', 'SET', 'DEL', 'EXISTS', 'EXPIRE', 'TTL',
      'HGET', 'HSET', 'HGETALL', 'HMGET', 'HMSET', 'HDEL',
      'ZADD', 'ZREM', 'ZSCORE', 'ZCARD', 'ZRANGE', 'ZREMRANGEBYSCORE',
      'LPUSH', 'RPUSH', 'LRANGE', 'LREM', 'LLEN',
      'SADD', 'SREM', 'SMEMBERS', 'SCARD',
      'SCAN', 'KEYS'
    ];
    return redisCommands.includes(command.toUpperCase());
  }

  /**
   * 명령어로부터 키 타입 추론
   */
  private inferKeyTypeFromCommand(command: string): string {
    const cmd = command.toUpperCase();

    if (['HGET', 'HSET', 'HGETALL', 'HMGET', 'HMSET', 'HDEL'].includes(cmd)) {
      return 'hash';
    }
    if (['ZADD', 'ZREM', 'ZSCORE', 'ZCARD', 'ZRANGE', 'ZREMRANGEBYSCORE'].includes(cmd)) {
      return 'zset';
    }
    if (['LPUSH', 'RPUSH', 'LRANGE', 'LREM', 'LLEN'].includes(cmd)) {
      return 'list';
    }
    if (['SADD', 'SREM', 'SMEMBERS', 'SCARD'].includes(cmd)) {
      return 'set';
    }

    return 'string';
  }

  /**
   * Lua용 유효한 Redis 키인지 검증 (더 엄격한 검증)
   */
  private isValidRedisKeyForLua(key: string): boolean {
    if (!key || key.length < 2) {
      return false;
    }

    // TypeScript와 동일한 엄격한 검증 적용
    return this.isValidRedisKey(key);
  }

  /**
   * 정규식 기반 백업 방식 (Lua AST 파싱 실패 시)
   */
  private extractLuaKeysWithRegex(content: string, filePath: string): RedisKeyInfo[] {
    const keys: RedisKeyInfo[] = [];
    const redisInstance = this.determineRedisInstance(filePath);

    // 기존 정규식 방식
    const keyPatterns = this.extractRedisKeyPatterns(content);

    for (const keyPattern of keyPatterns) {
      const keyInfo = this.analyzeKeyPattern(keyPattern, content, path.basename(filePath));
      if (keyInfo) {
        keys.push({
          ...keyInfo,
          redisInstance,
          luaScriptPath: filePath,
          keyType: this.inferKeyTypeFromCode(keyPattern)
        });
      }
    }

    return keys;
  }

  /**
   * TypeScript 파일 분석
   */
  private async analyzeTsFile(filePath: string): Promise<RedisKeyInfo[]> {
    const content = await fs.readFile(filePath, 'utf-8');
    const keys: RedisKeyInfo[] = [];

    // Redis 키 패턴 추출 (문자열 리터럴에서)
    const keyPatterns = this.extractTsRedisKeyPatterns(content);

    for (const keyPattern of keyPatterns) {
      const keyInfo = this.analyzeKeyPattern(keyPattern, content, path.basename(filePath));
      if (keyInfo) {
        keys.push({
          ...keyInfo,
          redisInstance: 'unknown', // TS 파일에서는 인스턴스 결정이 어려움
          tsFilePath: filePath,
          keyType: this.inferKeyTypeFromCode(keyPattern)
        });
      }
    }

    return keys;
  }

  /**
   * Redis 인스턴스 결정
   */
  private determineRedisInstance(filePath: string): string {
    const normalizedPath = filePath.replace(/\\/g, '/').toLowerCase();

    if (normalizedPath.includes('/usercache/')) {
      return 'userCache';
    } else if (normalizedPath.includes('/monitor/')) {
      return 'monitor';
    } else if (normalizedPath.includes('/order/')) {
      return 'order';
    } else if (normalizedPath.includes('/nation/')) {
      return 'nation';
    } else if (normalizedPath.includes('/auth/')) {
      return 'auth';
    } else if (normalizedPath.includes('/user/')) {
      return 'user';
    }

    return 'unknown';
  }

  /**
   * Redis 키 패턴 추출
   */
  private extractRedisKeyPatterns(content: string): string[] {
    const patterns: string[] = [];

    // 문자열 연결로 만들어지는 키 패턴 찾기 (userId 제외)
    const keyPatternRegex = /['"`]([^'"`]*(?:account|gnid|nid|pubId|accountId)[^'"`]*)['"`]/gi;
    let match;

    while ((match = keyPatternRegex.exec(content)) !== null) {
      const pattern = match[1];
      if (pattern && this.isValidKeyPattern(pattern)) {
        patterns.push(pattern);
      }
    }

    // 변수로 구성된 키 패턴 찾기
    const variableKeyRegex = /local\s+\w*[Kk]ey\s*=\s*['"`]([^'"`]+)['"`]\s*\.\.\s*\w+/gi;
    while ((match = variableKeyRegex.exec(content)) !== null) {
      const basePattern = match[1];
      patterns.push(basePattern + ':*');
    }

    // redis.call에서 직접 사용되는 키 패턴 (userId 제외)
    const redisCallRegex = /redis\.call\([^,]+,\s*['"`]([^'"`]*(?:account|gnid|nid|pubId|accountId)[^'"`]*)['"`]/gi;
    while ((match = redisCallRegex.exec(content)) !== null) {
      const pattern = match[1] || '';
      if (this.isValidKeyPattern(pattern)) {
        patterns.push(pattern);
      }
    }

    return [...new Set(patterns)];
  }

  /**
   * TypeScript에서 Redis 키 패턴 추출 (AST 기반)
   */
  private extractTsRedisKeyPatterns(content: string): string[] {
    try {
      return this.extractRedisKeysWithAST(content);
    } catch (error) {
      console.warn('AST 파싱 실패, 정규식 방식으로 대체:', error);
      return this.extractRedisKeysWithRegex(content);
    }
  }

  /**
   * TypeScript AST를 사용한 정확한 Redis 키 패턴 추출
   */
  private extractRedisKeysWithAST(content: string): string[] {
    const patterns: string[] = [];

    // TypeScript 소스 파일 생성
    const sourceFile = ts.createSourceFile(
      'temp.ts',
      content,
      ts.ScriptTarget.Latest,
      true
    );

    // AST 순회하여 Redis 관련 호출 찾기
    const visit = (node: ts.Node) => {
      // 메서드 호출 표현식에서만 키 패턴 찾기
      if (ts.isCallExpression(node)) {
        this.analyzeCallExpression(node, patterns);
      }

      // 템플릿 리터럴은 Redis 호출 컨텍스트에서만 분석
      // (현재는 제외 - 호출 컨텍스트 확인이 어려움)

      // 자식 노드들 순회
      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
    return [...new Set(patterns)];
  }

  /**
   * 메서드 호출 표현식 분석
   */
  private analyzeCallExpression(node: ts.CallExpression, patterns: string[]): void {
    const expression = node.expression;

    // redis.method() 또는 client.method() 패턴 찾기
    if (ts.isPropertyAccessExpression(expression)) {
      const objectName = expression.expression.getText();
      const methodName = expression.name.text;

      // Redis 메서드인지 확인
      const redisObjectNames = ['redis', 'client', 'redisClient'];
      const redisMethods = ['get', 'set', 'del', 'hget', 'hset', 'hgetall', 'hmget', 'hmset', 'hdel',
                           'zadd', 'zrem', 'zscore', 'zcard', 'lrange', 'lpush', 'rpush', 'lrem',
                           'sadd', 'srem', 'smembers', 'exists', 'expire', 'ttl', 'scan', 'keys'];

      if (redisObjectNames.includes(objectName) && redisMethods.includes(methodName)) {
        // 첫 번째 인수가 문자열 리터럴인지 확인
        if (node.arguments.length > 0) {
          const firstArg = node.arguments[0];
          if (firstArg) {
            const keyPattern = this.extractStringFromNode(firstArg);
            if (keyPattern && this.isValidRedisKey(keyPattern)) {
              patterns.push(keyPattern);
            }
          }
        }
      }
    }
  }



  /**
   * AST 노드에서 문자열 값 추출 (Redis 호출 컨텍스트에서만)
   */
  private extractStringFromNode(node: ts.Node): string | null {
    // 단순 문자열 리터럴만 처리
    if (ts.isStringLiteral(node)) {
      return node.text;
    }

    // 템플릿 리터럴 처리
    if (ts.isNoSubstitutionTemplateLiteral(node)) {
      return node.text;
    }

    if (ts.isTemplateExpression(node)) {
      // 템플릿 리터럴의 텍스트 부분들을 조합
      let templateText = node.head.text;
      node.templateSpans.forEach((span, index) => {
        templateText += `{${index}}` + span.literal.text;
      });
      return templateText;
    }

    // 문자열 연결은 Redis 호출 컨텍스트가 확실한 경우만 처리
    if (ts.isBinaryExpression(node) && node.operatorToken.kind === ts.SyntaxKind.PlusToken) {
      const left = this.extractStringFromNode(node.left);
      const right = this.extractStringFromNode(node.right);

      if (left && right) {
        return left + right;
      } else if (left && this.isRedisKeyPrefix(left)) {
        // Redis 키 접두사인 경우만 플레이스홀더 추가
        return left + '{id}';
      }
    }

    return null;
  }

  /**
   * Redis 키 접두사인지 확인
   */
  private isRedisKeyPrefix(prefix: string): boolean {
    // 콜론으로 끝나는 Redis 키 접두사 패턴
    return /^[a-zA-Z][a-zA-Z0-9_]*:$/.test(prefix);
  }

  /**
   * 유효한 Redis 키인지 검증 (더 엄격한 검증)
   */
  private isValidRedisKey(key: string): boolean {
    if (!key || key.length < 3) {
      return false;
    }

    // Redis 키 패턴의 특징을 확인
    if (!this.hasRedisKeyCharacteristics(key)) {
      return false;
    }

    // 로그 메시지나 일반 문장 제외
    if (this.isLogMessageOrSentence(key)) {
      return false;
    }

    // Redis 키 관련 키워드 포함 여부 (더 엄격하게)
    const redisKeywords = /(?:account|gnid|nid|pubId|accountId|user|town|nation|guild|cache|session|token|auth)(?:[:\-_]|$)/i;
    if (!redisKeywords.test(key)) {
      return false;
    }

    return true;
  }

  /**
   * Redis 키의 특징을 가지고 있는지 확인
   */
  private hasRedisKeyCharacteristics(key: string): boolean {
    // Redis 키는 보통 다음 특징을 가짐:
    // 1. 콜론(:), 하이픈(-), 언더스코어(_) 등의 구분자 포함
    // 2. 짧고 간결한 형태
    // 3. 공백이 많지 않음

    const hasDelimiters = /[:\-_]/.test(key);
    const isReasonableLength = key.length <= 100; // 너무 긴 문자열 제외
    const hasMinimalSpaces = (key.match(/\s/g) || []).length <= 3; // 공백이 3개 이하

    return hasDelimiters && isReasonableLength && hasMinimalSpaces;
  }

  /**
   * 로그 메시지나 일반 문장인지 확인
   */
  private isLogMessageOrSentence(key: string): boolean {
    // 로그 메시지나 일반 문장의 특징:
    // 1. 여러 단어로 구성된 문장
    // 2. "to", "new", "the", "and" 등의 일반적인 영어 단어 포함
    // 3. 동사 + 명사 구조

    const commonWords = /\b(to|new|the|and|or|is|are|was|were|has|have|will|would|should|could|can|may|might|must|shall|publishing|creating|updating|deleting|sending|receiving|processing|handling|managing|executing|running|starting|stopping|ending|beginning|finishing|completing)\b/i;
    const hasMultipleWords = key.split(/\s+/).length >= 3;
    const looksLikeSentence = /^[a-z].*\s.*[a-z]$/i.test(key);

    return commonWords.test(key) && (hasMultipleWords || looksLikeSentence);
  }

  /**
   * 정규식 기반 백업 방식 (AST 파싱 실패 시)
   * 실제 Redis 호출 컨텍스트에서만 키 패턴 추출
   */
  private extractRedisKeysWithRegex(content: string): string[] {
    const patterns: string[] = [];

    // 실제 Redis 메서드 호출에서만 키 패턴 찾기
    this.extractRedisMethodKeys(content, patterns);

    // 문자열 연결이나 템플릿 리터럴은 AST에서 처리하지 못한 경우에만
    // 실제 Redis 호출 컨텍스트가 확실한 경우만 추출
    // (현재는 제외 - AST가 더 정확함)

    return [...new Set(patterns)];
  }

  /**
   * Redis 메서드 호출에서 키 패턴 추출
   */
  private extractRedisMethodKeys(content: string, patterns: string[]): void {
    // Redis 메서드 호출 패턴 (get, set, hget, hset, zadd, etc.)
    const redisMethodRegex = /(?:redis|client)\.(?:get|set|del|hget|hset|hgetall|hmget|hmset|hdel|zadd|zrem|zscore|zcard|lrange|lpush|rpush|lrem|sadd|srem|smembers|exists|expire|ttl|scan|keys)\s*\(\s*['"`]([^'"`]+)['"`]/gi;
    let match;

    while ((match = redisMethodRegex.exec(content)) !== null) {
      const key = match[1];
      if (key && this.isRedisKeyPattern(key)) {
        patterns.push(key);
      }
    }
  }



  /**
   * Redis 키 패턴인지 검증 (더 엄격한 검증)
   */
  private isRedisKeyPattern(pattern: string): boolean {
    if (!pattern || pattern.length < 3) {
      return false;
    }

    // Redis 키 관련 키워드가 포함되어야 함
    const redisKeywords = /(?:account|gnid|nid|pubId|accountId|user|town|nation|guild|cache|session|token|auth|login|logout|register|signup|signin|password|email|phone|address|name|title|description|content|text|message|comment|note|memo|tag|label|category|group|team|member|player|character|avatar|profile|id|uid|gid|pid|sid|tid|cid|did|fid|lid|rid|vid|wid|xid|yid|zid)/i;

    if (!redisKeywords.test(pattern)) {
      return false;
    }

    // 일반적인 코드 패턴 제외
    const excludePatterns = [
      // 로그 메시지나 일반 문자열
      /^[A-Z_]+$/,  // 상수
      /\s/,         // 공백 포함
      /[{}()[\]]/,  // 코드 블록
      /^(if|then|else|end|for|while|do|function|local|return|break|continue|true|false|nil|null|undefined|var|let|const|class|interface|enum|struct|union|typedef|namespace|using|import|export|require|include|define)$/i,
      /^(get|set|del|add|remove|update|create|delete|find|search|list|count|check|validate|process|handle|manage|execute|run|start|stop|end|begin|finish|complete|success|error|fail|warning|info|debug|log|trace)$/i,
    ];

    return !excludePatterns.some(regex => regex.test(pattern));
  }

  /**
   * 키 패턴이 유효한지 확인
   */
  private isValidKeyPattern(pattern: string): boolean {
    // 너무 짧거나 의미없는 패턴 제외
    if (pattern.length < 3) return false;

    // 특정 키워드가 포함된 패턴만 포함 (확장된 키워드 목록)
    const relevantKeywords = [
      'account', 'user', 'gnid', 'nid', 'pubId', 'accountId', 'prologue',
      'town', 'nation', 'guild', 'investment', 'report', 'deletion'
    ];

    const hasRelevantKeyword = relevantKeywords.some(keyword =>
      pattern.toLowerCase().includes(keyword.toLowerCase())
    );

    if (!hasRelevantKeyword) return false;

    // ID 관련 키워드가 포함된 패턴만 허용
    const idKeywords = ['nid', 'pubId', 'accountId', 'gnid', 'userId'];
    const hasIdKeyword = idKeywords.some(keyword =>
      pattern.toLowerCase().includes(keyword.toLowerCase()) ||
      pattern.includes(`{${keyword}}`)
    );

    return hasIdKeyword;
  }

  /**
   * 키 패턴 분석
   */
  private analyzeKeyPattern(keyPattern: string, content: string, fileName: string): RedisKeyInfo | null {
    const lowerPattern = keyPattern.toLowerCase();

    // 명확하지 않은 패턴 제외 (너무 짧거나 의미없는 패턴)
    if (keyPattern.length < 3 || this.isInvalidKeyPattern(keyPattern)) {
      return null;
    }

    // 각 식별자 사용 여부 확인 (더 엄격한 검증)
    const usesAccountId = this.checkAccountIdUsage(content, lowerPattern);
    const usesPubId = this.checkPubIdUsage(content, lowerPattern);
    const usesGnid = this.checkGnidUsage(content, lowerPattern);
    const usesNid = this.checkNidUsage(content, lowerPattern);

    // 관련 없는 패턴 제외 (GNID/NID만 체크, userId 제외)
    if (!usesAccountId && !usesPubId && !usesGnid && !usesNid) {
      return null;
    }

    return {
      keyPattern,
      redisInstance: 'unknown', // 호출자에서 설정
      usesAccountId,
      usesPubId,
      usesUserId: false, // userId는 리맵핑 대상이 아니므로 항상 false
      usesGnid,
      usesNid,
      description: this.generateKeyDescription(keyPattern, fileName),
    };
  }

  /**
   * 유효하지 않은 키 패턴 검사
   */
  private isInvalidKeyPattern(pattern: string): boolean {
    const lowerPattern = pattern.toLowerCase();

    // 제외할 패턴들
    const excludePatterns = [
      // 일반적인 단어들 (리맵핑과 무관)
      /^(get|set|del|add|remove|update|create|delete|find|search|list|count|check|validate|process|handle|manage|execute|run|start|stop|end|begin|finish|complete|success|error|fail|warning|info|debug|log|trace|data|info|config|setting|option|param|value|result|response|request|status|state|mode|type|kind|sort|order|group|category|class|level|rank|score|point|time|date|hour|minute|second|day|week|month|year|now|today|yesterday|tomorrow|current|last|next|first|final|total|sum|max|min|avg|count|number|amount|size|length|width|height|depth|weight|volume|area|distance|speed|rate|ratio|percent|index|position|location|place|region|zone|area|section|part|piece|item|element|component|module|system|service|server|client|database|table|column|row|record|field|key|value|cache|session|cookie|token|auth|login|logout|register|signup|signin|password|email|phone|address|name|title|description|content|text|message|comment|note|memo|tag|label|category|group|team|user|admin|guest|member|player|character|avatar|profile|account|id|uid|gid|pid|sid|tid|cid|did|fid|lid|rid|vid|wid|xid|yid|zid)$/,

      // 코드 조각들
      /^(if|then|else|end|for|while|do|function|local|return|break|continue|true|false|nil|null|undefined|var|let|const|class|interface|enum|struct|union|typedef|namespace|using|import|export|require|include|define|ifdef|ifndef|endif|pragma|error|warning|info|debug|trace|log|print|console|alert|confirm|prompt|window|document|element|node|object|array|string|number|boolean|date|time|math|random|json|xml|html|css|js|ts|php|java|cpp|cs|py|rb|go|rs|swift|kt|scala|clj|hs|ml|fs|vb|pl|sh|bat|cmd|ps1|sql|lua|r|m|mm|h|hpp|c|cc|cxx|cpp|c\+\+|asm|s|S|ld|ar|nm|objdump|readelf|strip|strings|file|grep|sed|awk|cut|sort|uniq|head|tail|wc|tr|tee|xargs|find|locate|which|whereis|whoami|id|groups|su|sudo|chmod|chown|chgrp|ls|ll|la|dir|cd|pwd|mkdir|rmdir|rm|cp|mv|ln|touch|cat|more|less|head|tail|vi|vim|nano|emacs|gedit|notepad|code|atom|sublime|idea|eclipse|netbeans|vscode|git|svn|hg|bzr|cvs|p4|tfs|make|cmake|ant|maven|gradle|npm|yarn|pip|gem|cargo|go|stack|cabal|lein|sbt|mix|rebar|hex|pub|composer|packagist|nuget|bower|jspm|webpack|rollup|parcel|gulp|grunt|brunch|browserify|requirejs|systemjs|amd|umd|cjs|esm|iife|babel|typescript|coffeescript|livescript|purescript|elm|reason|bucklescript|clojurescript|dart|kotlin|scala|groovy|jruby|jython|ironpython|ironruby|fsharp|vbnet|csharp|visualbasic|powershell|bash|zsh|fish|tcsh|csh|ksh|dash|ash|busybox|alpine|ubuntu|debian|centos|rhel|fedora|opensuse|arch|gentoo|slackware|freebsd|openbsd|netbsd|dragonfly|solaris|aix|hpux|irix|tru64|qnx|vxworks|rtems|freertos|zephyr|contiki|tinyos|riot|mynewt|nuttx|chibios|embos|threadx|safertos|integrity|lynxos|vxworks653|pikeos|qnx|rtlinux|xenomai|rtai|preemptrt|windows|macos|linux|android|ios|watchos|tvos|tizen|sailfish|ubuntu|debian|centos|rhel|fedora|opensuse|arch|gentoo|slackware|freebsd|openbsd|netbsd|dragonfly|solaris|aix|hpux|irix|tru64|qnx|vxworks|rtems|freertos|zephyr|contiki|tinyos|riot|mynewt|nuttx|chibios|embos|threadx|safertos|integrity|lynxos|vxworks653|pikeos|qnx|rtlinux|xenomai|rtai|preemptrt)$/,

      // 너무 일반적인 패턴들
      /^(test|temp|tmp|cache|session|config|setting|option|param|data|info|log|debug|error|warning|success|fail|result|response|request|status|state|mode|type|kind|sort|order|group|category|class|level|rank|score|point|time|date|current|last|next|first|final|total|count|number|size|length|index|position|location|place|region|zone|area|section|part|item|element|component|module|system|service|server|client|database|table|column|row|record|field|key|value)$/,
    ];

    return excludePatterns.some(regex => regex.test(lowerPattern));
  }

  /**
   * AccountId 사용 여부 엄격 검증
   */
  private checkAccountIdUsage(content: string, lowerPattern: string): boolean {
    // 패턴에 account가 포함되어 있거나, 코드에서 accountId 관련 변수/함수를 사용하는 경우
    return lowerPattern.includes('account') ||
           /\b(accountid|account_id|gnid)\b/i.test(content);
  }

  /**
   * PubId 사용 여부 엄격 검증
   */
  private checkPubIdUsage(content: string, lowerPattern: string): boolean {
    // 패턴에 pub가 포함되어 있거나, 코드에서 pubId 관련 변수/함수를 사용하는 경우
    return lowerPattern.includes('pub') ||
           /\b(pubid|pub_id|nid)\b/i.test(content);
  }



  /**
   * GNID 사용 여부 엄격 검증
   */
  private checkGnidUsage(content: string, lowerPattern: string): boolean {
    return lowerPattern.includes('gnid') || /\bgnid\b/i.test(content);
  }

  /**
   * NID 사용 여부 엄격 검증
   */
  private checkNidUsage(content: string, lowerPattern: string): boolean {
    return lowerPattern.includes('nid') || /\bnid\b/i.test(content);
  }

  /**
   * 키 설명 생성
   */
  private generateKeyDescription(keyPattern: string, fileName: string): string {
    const lowerPattern = keyPattern.toLowerCase();

    if (lowerPattern.includes('prologue')) {
      return `${fileName}: 프롤로그 상태 관리`;
    } else if (lowerPattern.includes('account')) {
      return `${fileName}: 계정 정보 관리`;
    } else if (lowerPattern.includes('user')) {
      return `${fileName}: 사용자 정보 관리`;
    } else if (lowerPattern.includes('token')) {
      return `${fileName}: 토큰 관리`;
    } else if (lowerPattern.includes('heartbeat')) {
      return `${fileName}: 하트비트 관리`;
    } else {
      return `${fileName}: ${keyPattern} 관련 데이터`;
    }
  }


}
