import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';
import { RemapRecord } from '../analyzer/remapDataSummaryAnalyzer';
import { WorldShardMapping, AnalysisResult, DatabaseTableInfo } from '../types';

export class DatabaseScriptGenerator {
  
  async generateScripts(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    console.log('🗄️ SQL 스크립트 생성 중...');

    // 사용자 데이터베이스별로 그룹화
    const shardGroups = this.groupRecordsByUserShard(records, worldMapping);

    for (const [shardIndex, shardRecords] of shardGroups.entries()) {
      if (shardRecords.length === 0) continue;

      const shardName = `user_shard_${shardIndex}`;
      await this.generateUserShardScript(shardRecords, shardName, outputDir, codebaseAnalysisResult);
    }

    // 월드 데이터베이스 스크립트 생성
    await this.generateWorldDatabaseScript(records, worldMapping, outputDir, codebaseAnalysisResult);

    console.log(`✅ SQL 스크립트 생성 완료 (${records.length}개 레코드)`);
  }

  async generateGlobalScripts(
    records: RemapRecord[],
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    console.log('🌐 전역 SQL 스크립트 생성 중 (auth만)...');

    // auth 데이터베이스 스크립트 생성
    await this.generateAuthDatabaseScript(records, outputDir, codebaseAnalysisResult);

    console.log(`✅ 전역 SQL 스크립트 생성 완료 (${records.length}개 레코드)`);
  }

  async generateUserShardScripts(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    console.log('👥 사용자 샤드 SQL 스크립트 생성 중...');

    // 사용자 데이터베이스별로 그룹화
    const shardGroups = this.groupRecordsByUserShard(records, worldMapping);

    for (const [shardIndex, shardRecords] of shardGroups.entries()) {
      if (shardRecords.length === 0) continue;

      const shardName = `user_shard_${shardIndex}`;
      await this.generateUserShardScript(shardRecords, shardName, outputDir, codebaseAnalysisResult);
    }

    console.log(`✅ 사용자 샤드 SQL 스크립트 생성 완료 (${records.length}개 레코드)`);
  }

  private groupRecordsByUserShard(
    records: RemapRecord[],
    worldMapping: WorldShardMapping
  ): Map<number, RemapRecord[]> {
    const shardGroups = new Map<number, RemapRecord[]>();

    // 설정에서 샤드 수를 가져오거나 기본값 사용
    // 현재는 모든 샤드에 모든 레코드를 포함 (샤딩하지 않음)
    const userShardCount = 4; // 기본값, 나중에 설정에서 가져오도록 개선 가능

    // 샤드 수만큼 초기화
    for (let i = 0; i < userShardCount; i++) {
      shardGroups.set(i, []);
    }

    // 모든 샤드에 모든 레코드를 포함 (GNID 기반 샤딩 제거)
    // 각 샤드는 독립적으로 모든 업데이트를 수행해야 함
    records.forEach(record => {
      for (let shardIndex = 0; shardIndex < userShardCount; shardIndex++) {
        const shardRecords = shardGroups.get(shardIndex) || [];
        shardRecords.push(record);
        shardGroups.set(shardIndex, shardRecords);
      }
    });

    return shardGroups;
  }

  private async generateUserShardScript(
    records: RemapRecord[],
    shardName: string,
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    const fileName = `${shardName}_update.sql`;
    const filePath = path.join(outputDir, fileName);

    let sqlContent = this.generateSqlHeader(shardName, records.length);

    // 업데이트된 테이블 목록 수집
    const updatedTables = this.getUpdatedTableNames(codebaseAnalysisResult, 'user');

    // 배치 단위로 처리
    const batchSize = 1000;
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);
      sqlContent += this.generateUserUpdateBatch(batch, i / batchSize + 1, codebaseAnalysisResult);
    }

    sqlContent += this.generateSqlFooter(updatedTables);

    await fs.writeFile(filePath, sqlContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드)`);
  }

  async generateWorldDatabaseScript(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    const fileName = `world_update.sql`;
    const filePath = path.join(outputDir, fileName);

    let sqlContent = this.generateSqlHeader(`world_${worldMapping.worldId}`, records.length);

    // 업데이트된 테이블 목록 수집
    const updatedTables = this.getUpdatedTableNames(codebaseAnalysisResult, 'world');

    // 월드 데이터베이스 테이블 업데이트
    sqlContent += this.generateWorldTableUpdates(records, codebaseAnalysisResult);

    sqlContent += this.generateSqlFooter(updatedTables);

    await fs.writeFile(filePath, sqlContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드)`);
  }

  private generateSqlHeader(databaseName: string, recordCount: number): string {
    const timestamp = new Date().toISOString();
    return `-- =====================================================
-- UWO GNID/NID 리맵핑 SQL 스크립트
-- 데이터베이스: ${databaseName}
-- 생성일시: ${timestamp}
-- 레코드 수: ${recordCount}
-- =====================================================
--
-- 주의사항:
-- 1. 인덱스 필드 업데이트로 인한 성능 최적화 적용
-- 2. 배치 단위 처리로 락 시간 최소화
-- 3. 실행 전 반드시 백업 수행 필요
-- 4. 트랜잭션 단위로 안전하게 처리
-- =====================================================

-- 세션 설정
SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';
SET SESSION innodb_lock_wait_timeout = 300;
SET SESSION lock_wait_timeout = 300;

-- 트랜잭션 시작
START TRANSACTION;

-- 안전 모드 해제 (대량 업데이트를 위해)
SET SQL_SAFE_UPDATES = 0;

-- 인덱스 최적화를 위한 설정
SET SESSION foreign_key_checks = 0;
SET SESSION unique_checks = 0;

`;
  }

  private generateSqlFooter(updatedTables?: string[]): string {
    let footer = `
-- 설정 복원
SET SESSION foreign_key_checks = 1;
SET SESSION unique_checks = 1;
SET SQL_SAFE_UPDATES = 1;

-- 통계 정보 업데이트 (성능 최적화)`;

    if (updatedTables && updatedTables.length > 0) {
      updatedTables.forEach(tableName => {
        footer += `\nANALYZE TABLE ${tableName};`;
      });
    } else {
      footer += `\n-- 주의: 업데이트된 테이블들에 대해 ANALYZE TABLE을 실행하세요`;
      footer += `\n-- 예: ANALYZE TABLE u_user, u_character, w_auto_sailings;`;
    }

    footer += `

-- 트랜잭션 커밋
COMMIT;

-- 완료 메시지 및 통계
SELECT
    'UWO GNID/NID 리맵핑 완료' AS status,
    NOW() AS completed_at,
    @@session.last_insert_id AS last_operation;

-- 실행 후 확인 쿼리 (선택사항)
-- 업데이트된 각 테이블에 대해 레코드 수를 확인하세요
`;

    return footer;
  }

  private generateUserUpdateBatch(records: RemapRecord[], batchNumber: number, codebaseAnalysisResult: AnalysisResult): string {
    let sql = `\n-- ===== 배치 ${batchNumber} =====\n`;
    sql += 'START TRANSACTION;\n';

    // 사용자 테이블 업데이트
    sql += this.generateUserTableUpdate(records, codebaseAnalysisResult);

    // 캐릭터 관련 테이블 업데이트
    sql += this.generateCharacterTableUpdates(records, codebaseAnalysisResult);

    sql += 'COMMIT;\n';
    return sql;
  }

  private generateUserTableUpdate(records: RemapRecord[], codebaseAnalysisResult: AnalysisResult): string {
    let sql = '\n-- 사용자 테이블 업데이트 (실제 스키마 기반)\n';

    // 분석된 테이블에서 user 데이터베이스의 테이블들 찾기
    const userTables = codebaseAnalysisResult.databaseTables.filter(table =>
      table.database === 'user' &&
      table.columns.some(col => col.relatedTo === 'accountId' || col.relatedTo === 'pubId')
    );

    if (userTables.length === 0) {
      sql += '-- 경고: GNID/NID 관련 컬럼을 가진 사용자 테이블이 분석되지 않았습니다.\n';
      sql += '-- 실제 데이터베이스에서 u_ prefix를 가진 테이블들을 확인하고 수동으로 스크립트를 작성해주세요.\n';
      return this.generateDefaultUserTableUpdate(records);
    }

    // GNID별로 그룹화하여 중복 업데이트 방지
    const gnidMap = new Map<string, string>();
    const nidMap = new Map<string, string>();
    records.forEach(record => {
      gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
      nidMap.set(record.uwo_Nid, record.uwogl_Nid);
    });

    // 실제 분석된 테이블별로 업데이트 생성
    userTables.forEach(table => {
      sql += `\n-- ${table.tableName} 테이블 업데이트\n`;

      const accountIdCol = table.columns.find(col => col.relatedTo === 'accountId');
      const pubIdCol = table.columns.find(col => col.relatedTo === 'pubId');

      if (accountIdCol && gnidMap.size > 0) {
        sql += this.generateCaseWhenUpdate(
          table.tableName,
          accountIdCol.columnName,
          gnidMap,
          'GNID'
        );
      }

      if (pubIdCol && nidMap.size > 0) {
        sql += this.generateCaseWhenUpdate(
          table.tableName,
          pubIdCol.columnName,
          nidMap,
          'NID'
        );
      }

      // 테이블별 특별 처리 규칙 적용
      const tableSpecificRules = this.getTableSpecificRules(table.tableName);

      sql += `-- ${table.tableName} 업데이트 결과 확인\n`;
      if (accountIdCol && !tableSpecificRules.skipAccountId) {
        sql += `SELECT COUNT(*) as updated_${table.tableName}_records FROM ${table.tableName} WHERE ${accountIdCol.columnName} IN (${Array.from(gnidMap.values()).map(v => `'${v}'`).join(', ')});\n`;
      } else if (pubIdCol && !tableSpecificRules.skipPubId) {
        sql += `SELECT COUNT(*) as updated_${table.tableName}_records FROM ${table.tableName} WHERE ${pubIdCol.columnName} IN (${Array.from(nidMap.values()).map(v => `'${v}'`).join(', ')});\n`;
      } else {
        sql += `-- ${table.tableName}: 검증 가능한 컬럼이 없습니다\n`;
      }
      sql += '\n';
    });

    return sql;
  }

  private generateCaseWhenUpdate(
    tableName: string,
    columnName: string,
    idMap: Map<string, string>,
    idType: string
  ): string {
    let sql = `UPDATE ${tableName}\n`;
    sql += `SET ${columnName} = CASE ${columnName}\n`;

    // CASE WHEN 구문 생성
    idMap.forEach((newId, oldId) => {
      sql += `    WHEN '${oldId}' THEN '${newId}'\n`;
    });

    sql += `END\n`;
    sql += `WHERE ${columnName} IN (\n`;

    // IN 절 생성 (20개씩 줄바꿈)
    const oldIds = Array.from(idMap.keys());
    const chunks = [];
    for (let i = 0; i < oldIds.length; i += 20) {
      const chunk = oldIds.slice(i, i + 20);
      chunks.push(`    ${chunk.map(id => `'${id}'`).join(', ')}`);
    }
    sql += chunks.join(',\n') + '\n';

    sql += ');\n';
    return sql;
  }

  private generateComplexCaseWhenUpdate(
    tableName: string,
    accountIdColumn: string,
    pubIdColumn: string,
    records: RemapRecord[]
  ): string {
    let sql = `UPDATE ${tableName}\n`;
    sql += `SET ${accountIdColumn} = CASE ${accountIdColumn}\n`;

    // accountId CASE WHEN 구문
    records.forEach(record => {
      sql += `    WHEN '${record.uwo_Gnid}' THEN '${record.uwogl_Gnid}'\n`;
    });
    sql += `END,\n`;

    // pubId CASE WHEN 구문
    sql += `    ${pubIdColumn} = CASE ${pubIdColumn}\n`;
    records.forEach(record => {
      sql += `    WHEN '${record.uwo_Nid}' THEN '${record.uwogl_Nid}'\n`;
    });
    sql += `END\n`;

    // WHERE 절 - accountId와 pubId 조합으로 매칭
    sql += `WHERE (${accountIdColumn}, ${pubIdColumn}) IN (\n`;
    const pairs = records.map(record =>
      `    ('${record.uwo_Gnid}', '${record.uwo_Nid}')`
    );
    sql += pairs.join(',\n') + '\n';
    sql += ');\n';

    return sql;
  }

  private generateDefaultUserTableUpdate(records: RemapRecord[]): string {
    let sql = '\n-- 경고: 분석된 사용자 테이블이 없습니다.\n';
    sql += '-- 실제 데이터베이스에 존재하는 테이블명을 확인하고 수동으로 수정해주세요.\n';
    sql += '-- 일반적으로 u_ prefix를 가진 테이블들을 확인하세요 (예: u_user, u_character 등)\n\n';

    const gnidMap = new Map<string, string>();
    const nidMap = new Map<string, string>();
    records.forEach(record => {
      gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
      nidMap.set(record.uwo_Nid, record.uwogl_Nid);
    });

    // 예시 템플릿 제공 (실제 테이블명으로 수정 필요)
    sql += '-- 예시: u_user 테이블의 accountId 필드 업데이트 (실제 테이블명으로 수정 필요)\n';
    sql += '-- UPDATE u_user SET accountId = CASE accountId\n';
    gnidMap.forEach((newGnid, oldGnid) => {
      sql += `--   WHEN '${oldGnid}' THEN '${newGnid}'\n`;
    });
    sql += '--   ELSE accountId\n';
    sql += '--   END\n';
    sql += `-- WHERE accountId IN (${Array.from(gnidMap.keys()).map(k => `'${k}'`).join(', ')});\n\n`;

    // pubId 업데이트 예시
    sql += '-- 예시: u_user 테이블의 pubId 필드 업데이트 (실제 테이블명으로 수정 필요)\n';
    sql += '-- UPDATE u_user SET pubId = CASE pubId\n';
    nidMap.forEach((newNid, oldNid) => {
      sql += `--   WHEN '${oldNid}' THEN '${newNid}'\n`;
    });
    sql += '--   ELSE pubId\n';
    sql += '--   END\n';
    sql += `-- WHERE pubId IN (${Array.from(nidMap.keys()).map(k => `'${k}'`).join(', ')});\n\n`;

    return sql;
  }

  private generateCharacterTableUpdates(records: RemapRecord[], codebaseAnalysisResult: AnalysisResult): string {
    let sql = '\n-- 캐릭터 관련 테이블 업데이트 (실제 분석된 스키마 기반)\n';

    // 분석된 테이블에서 user 데이터베이스의 테이블들 중 accountId와 pubId를 모두 가진 테이블들 찾기
    const characterTables = codebaseAnalysisResult.databaseTables.filter(table =>
      table.database === 'user' &&
      table.columns.some(col => col.relatedTo === 'accountId') &&
      table.columns.some(col => col.relatedTo === 'pubId')
    );

    if (characterTables.length === 0) {
      sql += '-- 경고: 분석된 캐릭터 테이블이 없습니다.\n';
      return sql;
    }

    characterTables.forEach(table => {
      sql += `\n-- ${table.tableName} 테이블 업데이트\n`;

      const accountIdCol = table.columns.find(col => col.relatedTo === 'accountId');
      const pubIdCol = table.columns.find(col => col.relatedTo === 'pubId');

      // 테이블별 특별 처리 규칙 적용
      const tableSpecificRules = this.getTableSpecificRules(table.tableName);

      if (accountIdCol && pubIdCol && !tableSpecificRules.skipAccountId && !tableSpecificRules.skipPubId) {
        sql += `-- 복합 업데이트: (${accountIdCol.columnName}, ${pubIdCol.columnName})\n`;
        sql += this.generateComplexCaseWhenUpdate(
          table.tableName,
          accountIdCol.columnName,
          pubIdCol.columnName,
          records
        );
      } else {
        sql += `-- ${table.tableName}: 테이블별 규칙에 의해 스킵되거나 필요한 컬럼이 없습니다\n`;
        if (tableSpecificRules.skipAccountId) {
          sql += `-- accountId 컬럼 스킵됨\n`;
        }
        if (tableSpecificRules.skipPubId) {
          sql += `-- pubId 컬럼 스킵됨\n`;
        }
        sql += '\n';
      }
    });

    return sql;
  }

  private generateWorldTableUpdates(records: RemapRecord[], codebaseAnalysisResult: AnalysisResult): string {
    let sql = '\n-- 월드 데이터베이스 테이블 업데이트 (실제 분석된 스키마 기반)\n';

    // 분석된 테이블에서 world 데이터베이스의 테이블들 찾기
    const worldTables = codebaseAnalysisResult.databaseTables.filter(table =>
      table.database === 'world' &&
      (table.columns.some(col => col.relatedTo === 'accountId') ||
       table.columns.some(col => col.relatedTo === 'pubId'))
    );

    if (worldTables.length === 0) {
      sql += '-- 경고: 분석된 월드 테이블이 없습니다.\n';
      return sql;
    }

    worldTables.forEach(table => {
      sql += `\n-- ${table.tableName} 테이블 업데이트\n`;

      const accountIdCol = table.columns.find(col => col.relatedTo === 'accountId');
      const pubIdCol = table.columns.find(col => col.relatedTo === 'pubId');

      // 테이블별 특별 처리 규칙
      const tableSpecificRules = this.getTableSpecificRules(table.tableName);

      if (accountIdCol && !tableSpecificRules.skipAccountId) {
        const gnidMap = new Map<string, string>();
        records.forEach(record => {
          gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
        });
        sql += this.generateCaseWhenUpdate(
          table.tableName,
          accountIdCol.columnName,
          gnidMap,
          'GNID'
        );
      }

      if (pubIdCol && !tableSpecificRules.skipPubId) {
        const nidMap = new Map<string, string>();
        records.forEach(record => {
          nidMap.set(record.uwo_Nid, record.uwogl_Nid);
        });
        sql += this.generateCaseWhenUpdate(
          table.tableName,
          pubIdCol.columnName,
          nidMap,
          'NID'
        );
      }

      sql += `-- ${table.tableName} 업데이트 결과 확인\n`;
      if (accountIdCol && !tableSpecificRules.skipAccountId) {
        sql += `SELECT COUNT(*) as updated_${table.tableName}_records FROM ${table.tableName} WHERE ${accountIdCol.columnName} IN (${records.map(r => `'${r.uwogl_Gnid}'`).join(', ')});\n`;
      } else if (pubIdCol && !tableSpecificRules.skipPubId) {
        sql += `SELECT COUNT(*) as updated_${table.tableName}_records FROM ${table.tableName} WHERE ${pubIdCol.columnName} IN (${records.map(r => `'${r.uwogl_Nid}'`).join(', ')});\n`;
      } else {
        sql += `-- ${table.tableName}: 검증 가능한 컬럼이 없습니다\n`;
      }
      sql += '\n';
    });

    return sql;
  }

  /**
   * 테이블별 특별 처리 규칙 반환
   *
   * 주의: 이 규칙들은 스키마 분석 결과와 실제 데이터베이스 구조가 일치하지 않을 때 사용됩니다.
   * 가능하면 extract-schema 명령으로 정확한 스키마를 다시 추출하는 것을 권장합니다.
   */
  private getTableSpecificRules(tableName: string): { skipAccountId: boolean; skipPubId: boolean } {
    const rules: { [key: string]: { skipAccountId: boolean; skipPubId: boolean } } = {
      // w_auto_sailings 테이블은 실제로 accountId 컬럼이 없음 (스키마 분석 오류)
      'w_auto_sailings': {
        skipAccountId: true,
        skipPubId: false
      },
      // 추가 테이블 규칙들을 여기에 정의
      // 예: 'w_some_table': { skipAccountId: false, skipPubId: true }
    };

    const rule = rules[tableName] || { skipAccountId: false, skipPubId: false };

    // 규칙이 적용되는 경우 경고 로그 출력
    if (rule.skipAccountId || rule.skipPubId) {
      console.warn(`⚠️ 테이블 ${tableName}에 특별 규칙 적용: skipAccountId=${rule.skipAccountId}, skipPubId=${rule.skipPubId}`);
      console.warn(`💡 정확한 스키마를 위해 'extract-schema' 명령을 실행하여 스키마를 다시 추출하세요.`);
    }

    return rule;
  }

  /**
   * 업데이트될 테이블 목록을 반환
   */
  private getUpdatedTableNames(codebaseAnalysisResult: AnalysisResult, databaseType?: string): string[] {
    if (!codebaseAnalysisResult?.databaseTables) {
      return [];
    }

    let tables = codebaseAnalysisResult.databaseTables;

    // 데이터베이스 타입으로 필터링
    if (databaseType) {
      tables = tables.filter(table => table.database === databaseType);
    }

    // accountId 또는 pubId 관련 컬럼이 있는 테이블만 선택
    const relevantTables = tables.filter(table =>
      table.columns.some(col => col.relatedTo === 'accountId') ||
      table.columns.some(col => col.relatedTo === 'pubId')
    );

    // 테이블별 규칙을 적용하여 실제로 업데이트될 테이블만 반환
    return relevantTables
      .filter(table => {
        const rules = this.getTableSpecificRules(table.tableName);
        const hasAccountId = table.columns.some(col => col.relatedTo === 'accountId');
        const hasPubId = table.columns.some(col => col.relatedTo === 'pubId');

        // 최소한 하나의 컬럼이라도 업데이트 가능해야 함
        return (hasAccountId && !rules.skipAccountId) || (hasPubId && !rules.skipPubId);
      })
      .map(table => table.tableName);
  }

  private async generateAuthDatabaseScript(
    records: RemapRecord[],
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    const fileName = 'auth_update.sql';
    const filePath = path.join(outputDir, fileName);

    let sqlContent = this.generateSqlHeader('auth', records.length);

    // 분석된 테이블에서 auth 데이터베이스의 테이블들 찾기
    const authTables = codebaseAnalysisResult.databaseTables.filter(table =>
      table.database === 'auth' &&
      (table.columns.some(col => col.relatedTo === 'accountId') ||
       table.columns.some(col => col.relatedTo === 'pubId'))
    );

    // 업데이트된 테이블 목록 수집
    const updatedTables = this.getUpdatedTableNames(codebaseAnalysisResult, 'auth');

    if (authTables.length === 0) {
      sqlContent += '-- 경고: 분석된 auth 테이블이 없습니다.\n';
    } else {
      sqlContent += this.generateDatabaseTableUpdates(records, authTables, 'auth');
    }

    sqlContent += this.generateSqlFooter(updatedTables);

    await fs.writeFile(filePath, sqlContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드, ${authTables.length}개 테이블)`);
  }

  private generateDatabaseTableUpdates(records: RemapRecord[], tables: DatabaseTableInfo[], databaseName: string): string {
    let sql = `\n-- ${databaseName} 데이터베이스 테이블 업데이트 (실제 분석된 스키마 기반)\n`;

    tables.forEach(table => {
      sql += `\n-- ${table.tableName} 테이블 업데이트\n`;

      const accountIdCol = table.columns.find(col => col.relatedTo === 'accountId');
      const pubIdCol = table.columns.find(col => col.relatedTo === 'pubId');

      // a_accounts 테이블의 특별 처리 (id 필드)
      if (table.tableName === 'a_accounts') {
        const idCol = table.columns.find(col => col.columnName === 'id');
        if (idCol) {
          const gnidMap = new Map<string, string>();
          records.forEach(record => {
            gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
          });
          sql += this.generateCaseWhenUpdate(
            table.tableName,
            'id',
            gnidMap,
            'GNID'
          );
        }
      }

      // 테이블별 특별 처리 규칙 적용
      const tableSpecificRules = this.getTableSpecificRules(table.tableName);

      if (accountIdCol && !tableSpecificRules.skipAccountId) {
        const gnidMap = new Map<string, string>();
        records.forEach(record => {
          gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
        });
        sql += this.generateCaseWhenUpdate(
          table.tableName,
          accountIdCol.columnName,
          gnidMap,
          'GNID'
        );
      }

      if (pubIdCol && !tableSpecificRules.skipPubId) {
        const nidMap = new Map<string, string>();
        records.forEach(record => {
          nidMap.set(record.uwo_Nid, record.uwogl_Nid);
        });
        sql += this.generateCaseWhenUpdate(
          table.tableName,
          pubIdCol.columnName,
          nidMap,
          'NID'
        );
      }

      sql += `-- ${table.tableName} 업데이트 결과 확인\n`;
      if (table.tableName === 'a_accounts') {
        sql += `SELECT COUNT(*) as updated_${table.tableName}_records FROM ${table.tableName} WHERE id IN (${records.map(r => `'${r.uwogl_Gnid}'`).join(', ')});\n`;
      } else if (accountIdCol && !tableSpecificRules.skipAccountId) {
        sql += `SELECT COUNT(*) as updated_${table.tableName}_records FROM ${table.tableName} WHERE ${accountIdCol.columnName} IN (${records.map(r => `'${r.uwogl_Gnid}'`).join(', ')});\n`;
      } else if (pubIdCol && !tableSpecificRules.skipPubId) {
        sql += `SELECT COUNT(*) as updated_${table.tableName}_records FROM ${table.tableName} WHERE ${pubIdCol.columnName} IN (${records.map(r => `'${r.uwogl_Nid}'`).join(', ')});\n`;
      } else {
        sql += `-- ${table.tableName}: 검증 가능한 컬럼이 없습니다\n`;
      }
      sql += '\n';
    });

    return sql;
  }
}
