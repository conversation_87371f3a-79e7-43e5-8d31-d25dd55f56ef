#!/usr/bin/env node

import { CliInterface } from './cli/cliInterface';

async function main(): Promise<void> {
  const cli = new CliInterface();
  await cli.run(process.argv);
}

process.on('unhandledRejection', (reason, promise) => {
  console.error('처리되지 않은 Promise 거부:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('처리되지 않은 예외:', error);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n작업이 중단되었습니다.');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n작업이 종료되었습니다.');
  process.exit(0);
});

main().catch((error) => {
  console.error('애플리케이션 실행 실패:', error);
  process.exit(1);
});
