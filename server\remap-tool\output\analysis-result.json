{"databaseTables": [{"tableName": "w_auto_sailings", "database": "world", "columns": [{"columnName": "userId", "dataType": "int", "isNullable": false, "isPrimaryKey": true, "isIndexed": true, "relatedTo": "none", "defaultValue": null, "fullType": "int", "position": 1}, {"columnName": "fleetIndex", "dataType": "tinyint", "isNullable": false, "isPrimaryKey": true, "isIndexed": true, "relatedTo": "none", "defaultValue": null, "fullType": "tinyint", "position": 2}, {"columnName": "accountId", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "accountId", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(32)", "position": 3}, {"columnName": "pubId", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "pubId", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(32)", "position": 4}, {"columnName": "startTimeUtc", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 5}, {"columnName": "destCmsId", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 6}, {"columnName": "destType", "dataType": "tinyint", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "tinyint", "position": 7}, {"columnName": "path", "dataType": "json", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "json", "position": 8}, {"columnName": "isOfflineSailingDeactivated", "dataType": "tinyint", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "tinyint", "position": 9}, {"columnName": "extra", "dataType": "json", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "json", "position": 10}, {"columnName": "optionForServer", "dataType": "json", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "json", "position": 11}], "primaryKeys": ["userId", "fleetIndex"], "indexes": [{"indexName": "PRIMARY", "columns": ["userId", "fleetIndex"], "isUnique": true, "isPrimary": true, "type": "BTREE"}], "hasAccountId": true, "hasPubId": true, "shardingRequired": false, "filePath": "extracted:uwo_world.w_auto_sailings", "engine": "InnoDB", "collation": "utf8mb4_unicode_ci"}, {"tableName": "u_users", "database": "user", "columns": [{"columnName": "id", "dataType": "int", "isNullable": false, "isPrimaryKey": true, "isIndexed": true, "relatedTo": "none", "defaultValue": null, "fullType": "int", "position": 1}, {"columnName": "pubId", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "pubId", "defaultValue": "", "fullType": "<PERSON><PERSON><PERSON>(32)", "position": 2}, {"columnName": "createTimeUtc", "dataType": "timestamp", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "CURRENT_TIMESTAMP", "fullType": "timestamp", "extra": "DEFAULT_GENERATED", "position": 3}, {"columnName": "name", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(40)", "position": 4}, {"columnName": "lang", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "en", "fullType": "<PERSON><PERSON><PERSON>(8)", "position": 5}, {"columnName": "nationCmsId", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 6}, {"columnName": "companyJobCmsId", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 7}, {"columnName": "lastRewardedAchievementPointCmsId", "dataType": "int", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "int", "position": 8}, {"columnName": "noviceS<PERSON><PERSON>ly<PERSON>ount", "dataType": "tinyint", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "tinyint", "position": 9}, {"columnName": "lastNoviceSupplyTimeUtc", "dataType": "timestamp", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1970-01-01 00:00:01", "fullType": "timestamp", "position": 10}, {"columnName": "energy", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 11}, {"columnName": "lastUpdateEnergyTimeUtc", "dataType": "timestamp", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1970-01-01 00:00:01", "fullType": "timestamp", "position": 12}, {"columnName": "usedExploreQuickModeCount", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 13}, {"columnName": "lastExploreQuickModeCountUpdateTimeUtc", "dataType": "timestamp", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1970-01-01 00:00:01", "fullType": "timestamp", "position": 14}, {"columnName": "usedExploreTicketCount", "dataType": "tinyint", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "tinyint", "position": 15}, {"columnName": "lastExploreTicketCountUpdateTimeUtc", "dataType": "timestamp", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1970-01-01 00:00:01", "fullType": "timestamp", "position": 16}, {"columnName": "palaceRoyalOrderCmsId", "dataType": "int", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "int", "position": 17}, {"columnName": "palaceRoyalOrderRnds", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(40)", "position": 18}, {"columnName": "lastRoyalOrderCompletedTimeUtc", "dataType": "timestamp", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1970-01-01 00:00:01", "fullType": "timestamp", "position": 19}, {"columnName": "palaceRoyalTitleOrderCmsId", "dataType": "int", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "int", "position": 20}, {"columnName": "palaceRoyalTitleOrderRnds", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(40)", "position": 21}, {"columnName": "contractedCollectorTownBuildingCmsId", "dataType": "int", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "int", "position": 22}, {"columnName": "guildId", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 23}, {"columnName": "lastGuildLeftTimeUtc", "dataType": "timestamp", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1970-01-01 00:00:01", "fullType": "timestamp", "position": 24}, {"columnName": "lastCompanyJobUpdateTimeUtc", "dataType": "timestamp", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1970-01-01 00:00:01", "fullType": "timestamp", "position": 25}, {"columnName": "countryIp", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(5)", "position": 26}, {"columnName": "lastReceiveHotTimeUtc", "dataType": "timestamp", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1970-01-01 00:00:01", "fullType": "timestamp", "position": 27}, {"columnName": "firstMateCmsId", "dataType": "int", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "int", "position": 28}, {"columnName": "lastUpdateNationTimeUtc", "dataType": "timestamp", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "timestamp", "position": 29}, {"columnName": "isAdmiralProfileOpened", "dataType": "tinyint", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "tinyint", "position": 30}, {"columnName": "isFlagShipProfileOpened", "dataType": "tinyint", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "tinyint", "position": 31}, {"columnName": "westShipBuildLevel", "dataType": "tinyint", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1", "fullType": "tinyint", "position": 32}, {"columnName": "westShipBuildExp", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 33}, {"columnName": "lastCashShopDailyProductsUpdateTimeUtc", "dataType": "timestamp", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "timestamp", "position": 34}, {"columnName": "freeLeaderMateSwitchCount", "dataType": "tinyint", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "tinyint", "position": 35}, {"columnName": "freeLastLeaderMateSwitchTimeUtc", "dataType": "timestamp", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "timestamp", "position": 36}, {"columnName": "orientShipBuildLevel", "dataType": "tinyint", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1", "fullType": "tinyint", "position": 37}, {"columnName": "orientShipBuildExp", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 38}, {"columnName": "accumInvestByGem", "dataType": "bigint", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "bigint", "position": 39}, {"columnName": "curCargoPresetId", "dataType": "tinyint", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1", "fullType": "tinyint", "position": 40}, {"columnName": "primeMinisterElectedCount", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 41}, {"columnName": "representedMateCmsId", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 42}, {"columnName": "lastPaidSmuggleEnterTownCmsId", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 43}, {"columnName": "lastSmuggleTransactionTownCmsId", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 44}, {"columnName": "isFirstFleetProfileOpened", "dataType": "tinyint", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "tinyint", "position": 45}, {"columnName": "isFriendlyBattleRequestable", "dataType": "tinyint", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "tinyint", "position": 46}], "primaryKeys": ["id"], "indexes": [{"indexName": "PRIMARY", "columns": ["id"], "isUnique": true, "isPrimary": true, "type": "BTREE"}], "hasAccountId": false, "hasPubId": true, "shardingRequired": true, "filePath": "extracted:uwo_user_00.u_users", "engine": "InnoDB", "collation": "utf8mb4_unicode_ci"}, {"tableName": "a_accounts", "database": "auth", "columns": [{"columnName": "id", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "accountId", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(32)", "position": 1}, {"columnName": "lastWorldId", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(32)", "position": 2}, {"columnName": "isOnline", "dataType": "tinyint", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "1", "fullType": "tinyint", "position": 3}, {"columnName": "<PERSON><PERSON><PERSON><PERSON>", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(64)", "position": 4}, {"columnName": "lastUserId", "dataType": "int", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "int", "position": 5}, {"columnName": "createTimeUtc", "dataType": "timestamp", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "timestamp", "position": 6}, {"columnName": "lastLoginTimeUtc", "dataType": "timestamp", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "timestamp", "position": 7}, {"columnName": "accessLevel", "dataType": "tinyint", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "tinyint", "position": 8}, {"columnName": "blockTimeUtcByAdmin", "dataType": "timestamp", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "timestamp", "position": 9}, {"columnName": "revokeTimeUtc", "dataType": "timestamp", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "timestamp", "position": 10}, {"columnName": "loginPlatform", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(10)", "position": 11}, {"columnName": "clientVersion", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(15)", "position": 12}], "primaryKeys": [], "indexes": [], "hasAccountId": true, "hasPubId": false, "shardingRequired": false, "filePath": "extracted:uwo_auth.a_accounts", "engine": "InnoDB", "collation": "utf8mb4_unicode_ci"}, {"tableName": "a_pub_ids", "database": "auth", "columns": [{"columnName": "accountId", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "accountId", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(32)", "position": 1}, {"columnName": "pubId", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "pubId", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(32)", "position": 2}, {"columnName": "worldId", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(32)", "position": 3}], "primaryKeys": [], "indexes": [], "hasAccountId": true, "hasPubId": true, "shardingRequired": false, "filePath": "extracted:uwo_auth.a_pub_ids", "engine": "InnoDB", "collation": "utf8mb4_unicode_ci"}, {"tableName": "a_world_users", "database": "auth", "columns": [{"columnName": "userId", "dataType": "int", "isNullable": false, "isPrimaryKey": true, "isIndexed": true, "relatedTo": "none", "defaultValue": null, "fullType": "int", "extra": "auto_increment", "position": 1}, {"columnName": "pubId", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": false, "isPrimaryKey": false, "isIndexed": true, "relatedTo": "pubId", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(32)", "position": 2}, {"columnName": "name", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": true, "isPrimaryKey": false, "isIndexed": true, "relatedTo": "none", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(40)", "position": 3}, {"columnName": "worldId", "dataType": "<PERSON><PERSON><PERSON>", "isNullable": true, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": null, "fullType": "<PERSON><PERSON><PERSON>(32)", "position": 4}, {"columnName": "nationCmsId", "dataType": "int", "isNullable": false, "isPrimaryKey": false, "isIndexed": false, "relatedTo": "none", "defaultValue": "0", "fullType": "int", "position": 5}], "primaryKeys": ["userId"], "indexes": [{"indexName": "IDX_a_world_users__pubId", "columns": ["pubId"], "isUnique": false, "isPrimary": false, "type": "BTREE"}, {"indexName": "PRIMARY", "columns": ["userId"], "isUnique": true, "isPrimary": true, "type": "BTREE"}, {"indexName": "UQ_a_world_users__name_worldId", "columns": ["name", "worldId"], "isUnique": true, "isPrimary": false, "type": "BTREE"}], "hasAccountId": false, "hasPubId": true, "shardingRequired": false, "filePath": "extracted:uwo_auth.a_world_users", "engine": "InnoDB", "collation": "utf8mb4_unicode_ci"}], "redisKeys": [{"keyPattern": "account:{accountId}", "redisInstance": "userCache", "usesAccountId": true, "usesPubId": false, "usesUserId": false, "usesGnid": true, "usesNid": false, "description": "계정 정보 저장 (토큰, 월드ID, 주문ID, 하트비트 등)", "keyType": "hash", "keyCount": 0}, {"keyPattern": "prologueGnids:{worldId}", "redisInstance": "order", "usesAccountId": false, "usesPubId": false, "usesUserId": false, "usesGnid": true, "usesNid": false, "description": "월드별 프롤로그 상태 유저 관리 (Sorted Set, gnid를 member로 사용)", "keyType": "zset", "keyCount": 0}, {"keyPattern": "deletionPubIds", "redisInstance": "auth", "usesAccountId": false, "usesPubId": true, "usesUserId": false, "usesGnid": false, "usesNid": true, "description": "삭제 대상 pubId 목록 관리 (List)", "keyType": "list", "keyCount": 0}, {"keyPattern": "townUserWeeklyInvestmentReport:{nid}", "redisInstance": "userCache", "usesAccountId": false, "usesPubId": true, "usesUserId": false, "usesGnid": false, "usesNid": true, "description": "도시 주간 투자 보고서 (nid 기반 캐시)", "keyType": "string", "keyCount": 0}, {"keyPattern": "user:{userId}", "redisInstance": "userCache", "usesAccountId": false, "usesPubId": true, "usesUserId": true, "usesGnid": false, "usesNid": true, "description": "유저 캐시 정보 (Hash 내부 pubId 필드 값 변경)", "keyType": "hash", "keyCount": 0}], "totalFilesAnalyzed": 3328, "sqlFilesAnalyzed": 0, "luaFilesAnalyzed": 525, "tsFilesAnalyzed": 2803, "analysisTime": 7412}