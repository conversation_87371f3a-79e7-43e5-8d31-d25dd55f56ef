const path = require('path');
const fs = require('fs-extra');

async function testPaths() {
  console.log('📁 경로 테스트 시작...');
  
  const baseDir = '../node';
  const redisScriptDir = path.join(baseDir, 'redis_script');
  const srcDir = path.join(baseDir, 'src');
  
  console.log(`Base directory: ${path.resolve(baseDir)}`);
  console.log(`Redis script directory: ${path.resolve(redisScriptDir)}`);
  console.log(`Source directory: ${path.resolve(srcDir)}`);
  
  // 디렉토리 존재 확인
  const redisExists = await fs.pathExists(redisScriptDir);
  const srcExists = await fs.pathExists(srcDir);
  
  console.log(`\n📋 디렉토리 존재 여부:`);
  console.log(`- Redis script dir: ${redisExists ? '✅' : '❌'}`);
  console.log(`- Source dir: ${srcExists ? '✅' : '❌'}`);
  
  if (redisExists) {
    const { glob } = require('glob');
    const luaFiles = await glob('**/*.lua', { cwd: redisScriptDir, absolute: true });
    console.log(`\n🔍 Lua 파일 수: ${luaFiles.length}`);
    if (luaFiles.length > 0) {
      console.log(`첫 번째 Lua 파일: ${luaFiles[0]}`);
    }
  }
  
  if (srcExists) {
    const { glob } = require('glob');
    const tsFiles = await glob('**/*.ts', { cwd: srcDir, absolute: true });
    console.log(`\n🔍 TypeScript 파일 수: ${tsFiles.length}`);
    if (tsFiles.length > 0) {
      console.log(`첫 번째 TS 파일: ${tsFiles[0]}`);
    }
  }
}

testPaths().catch(console.error);
