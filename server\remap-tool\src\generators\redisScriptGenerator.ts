import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';
import { RemapRecord } from '../analyzer/remapDataSummaryAnalyzer';
import { WorldShardMapping } from '../types';

export class RedisScriptGenerator {
  
  async generateScripts(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    outputDir: string,
    codebaseAnalysisResult: any
  ): Promise<void> {
    console.log('🔴 Redis Lua 스크립트 생성 중...');

    // 분석된 Redis 키 패턴을 기반으로 스크립트 생성
    if (codebaseAnalysisResult.redisKeys && codebaseAnalysisResult.redisKeys.length > 0) {
      await this.generateAnalyzedRedisScripts(records, outputDir, codebaseAnalysisResult);
    } else {
      // 분석 결과가 없으면 기본 스크립트 생성
      console.log('⚠️ 분석된 Redis 키 패턴이 없습니다. 기본 스크립트를 생성합니다.');
      await this.generateDefaultRedisScripts(records, outputDir);
    }

    console.log(`✅ Redis Lua 스크립트 생성 완료 (${records.length}개 레코드)`);

    // Redis Shell script 생성
    await this.generateRedisShellScripts(records, worldMapping, outputDir, codebaseAnalysisResult);
  }

  async generateGlobalScripts(
    records: RemapRecord[],
    outputDir: string,
    codebaseAnalysisResult: any
  ): Promise<void> {
    console.log('🌐 전역 Redis Lua 스크립트 생성 중 (auth, userCache, order)...');

    // 전역 Redis 인스턴스들 (월드별로 분리되지 않음)
    const globalRedisInstances = ['auth', 'userCache', 'order'];

    if (codebaseAnalysisResult.redisKeys && codebaseAnalysisResult.redisKeys.length > 0) {
      // 분석된 Redis 키 패턴에서 전역 인스턴스만 필터링
      const globalPatterns = codebaseAnalysisResult.redisKeys.filter((pattern: any) =>
        globalRedisInstances.includes(pattern.redisInstance)
      );

      if (globalPatterns.length > 0) {
        await this.generateGlobalRedisScripts(records, outputDir, globalPatterns);
      } else {
        console.log('⚠️ 분석된 전역 Redis 키 패턴이 없습니다.');
      }
    } else {
      console.log('⚠️ 분석된 Redis 키 패턴이 없습니다.');
    }

    console.log(`✅ 전역 Redis Lua 스크립트 생성 완료 (${records.length}개 레코드)`);
  }

  private async generateAnalyzedRedisScripts(records: RemapRecord[], outputDir: string, codebaseAnalysisResult: any): Promise<void> {
    console.log(`🔍 분석된 ${codebaseAnalysisResult.redisKeys.length}개 Redis 키 패턴 기반 스크립트 생성`);

    // Redis 인스턴스별로 그룹화
    const redisInstances = new Map<string, any[]>();

    codebaseAnalysisResult.redisKeys.forEach((pattern: any) => {
      const instanceName = pattern.redisInstance || 'default';
      if (!redisInstances.has(instanceName)) {
        redisInstances.set(instanceName, []);
      }
      redisInstances.get(instanceName)!.push(pattern);
    });

    // 각 Redis 인스턴스별로 스크립트 생성
    for (const [instanceName, patterns] of redisInstances.entries()) {
      await this.generateRedisInstanceScript(records, outputDir, instanceName, patterns);
    }
  }

  private async generateRedisInstanceScript(records: RemapRecord[], outputDir: string, instanceName: string, patterns: any[]): Promise<void> {
    const fileName = `${instanceName}_redis_update.lua`;
    const filePath = path.join(outputDir, fileName);

    let luaContent = this.generateLuaHeader(`${instanceName} Redis`, records.length);

    luaContent += `
-- ${instanceName} Redis 업데이트 함수 (분석된 패턴 기반)
local function update_${instanceName}_redis(old_gnid, old_nid, new_gnid, new_nid)
    local updated_keys = 0

    -- SCAN 기반 업데이트 함수
    local function scan_and_update_keys(pattern, old_value, new_value)
        local cursor = "0"
        local count = 0

        repeat
            local result = redis.call('SCAN', cursor, 'MATCH', pattern, 'COUNT', 100)
            cursor = result[1]
            local keys = result[2]

            for _, key in ipairs(keys) do
                local key_type = redis.call('TYPE', key)['ok']
                local new_key = string.gsub(key, old_value, new_value)

                if key_type == 'string' then
                    local value = redis.call('GET', key)
                    if value then
                        redis.call('SET', new_key, value)
                        redis.call('DEL', key)
                        count = count + 1
                    end
                else
                    redis.call('RENAME', key, new_key)
                    count = count + 1
                end
            end
        until cursor == "0"

        return count
    end

    -- 분석된 키 패턴들
`;

    // 분석된 패턴들을 기반으로 업데이트 로직 생성
    patterns.forEach((pattern: any, index: number) => {
      luaContent += `    -- 패턴 ${index + 1}: ${pattern.keyPattern}\n`;
      if (pattern.usesAccountId) {
        luaContent += `    updated_keys = updated_keys + scan_and_update_keys("${pattern.keyPattern.replace('{accountId}', '" .. old_gnid .. "')}", old_gnid, new_gnid)\n`;
      }
      if (pattern.usesPubId) {
        luaContent += `    updated_keys = updated_keys + scan_and_update_keys("${pattern.keyPattern.replace('{pubId}', '" .. old_nid .. "')}", old_nid, new_nid)\n`;
      }
    });

    luaContent += `
    return updated_keys
end

local total_updated = 0

`;

    records.forEach((record, index) => {
      luaContent += `total_updated = total_updated + update_${instanceName}_redis("${record.uwo_Gnid}", "${record.uwo_Nid}", "${record.uwogl_Gnid}", "${record.uwogl_Nid}")\n`;
    });

    luaContent += this.generateLuaFooter();

    await fs.writeFile(filePath, luaContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드, ${patterns.length}개 패턴)`);
  }

  private async generateDefaultRedisScripts(records: RemapRecord[], outputDir: string): Promise<void> {
    // 기존 기본 스크립트 생성 로직
    await this.generateUserCacheScript(records, outputDir);
    await this.generateUserRedisScript(records, outputDir);
    await this.generateTownRedisScript(records, outputDir);
    await this.generateNationRedisScript(records, outputDir);
    await this.generateGuildRedisScript(records, outputDir);
    await this.generateRankingRedisScript(records, outputDir);
  }

  private async generateGlobalRedisScripts(records: RemapRecord[], outputDir: string, globalPatterns: any[]): Promise<void> {
    // Redis 인스턴스별로 그룹화
    const redisInstances = new Map<string, any[]>();

    globalPatterns.forEach((pattern: any) => {
      const instanceName = pattern.redisInstance;
      if (!redisInstances.has(instanceName)) {
        redisInstances.set(instanceName, []);
      }
      redisInstances.get(instanceName)!.push(pattern);
    });

    // 각 전역 Redis 인스턴스별로 스크립트 생성
    for (const [instanceName, patterns] of redisInstances.entries()) {
      await this.generateGlobalRedisInstanceScript(records, outputDir, instanceName, patterns);
    }
  }

  private async generateGlobalRedisInstanceScript(records: RemapRecord[], outputDir: string, instanceName: string, patterns: any[]): Promise<void> {
    const fileName = `${instanceName}_redis_update.lua`;
    const filePath = path.join(outputDir, fileName);

    let luaContent = this.generateLuaHeader(`${instanceName} Redis (전역)`, records.length);

    luaContent += `
-- ${instanceName} Redis 업데이트 함수 (전역, 분석된 패턴 기반)
local function update_${instanceName}_redis(old_gnid, old_nid, new_gnid, new_nid)
    local updated_keys = 0

    -- SCAN 기반 업데이트 함수
    local function scan_and_update_keys(pattern, old_value, new_value)
        local cursor = "0"
        local count = 0

        repeat
            local result = redis.call('SCAN', cursor, 'MATCH', pattern, 'COUNT', 100)
            cursor = result[1]
            local keys = result[2]

            for _, key in ipairs(keys) do
                local key_type = redis.call('TYPE', key)['ok']
                local new_key = string.gsub(key, old_value, new_value)

                if key_type == 'string' then
                    local value = redis.call('GET', key)
                    if value then
                        redis.call('SET', new_key, value)
                        redis.call('DEL', key)
                        count = count + 1
                    end
                else
                    redis.call('RENAME', key, new_key)
                    count = count + 1
                end
            end
        until cursor == "0"

        return count
    end

    -- 분석된 키 패턴들 (${instanceName})
`;

    // 분석된 패턴들을 기반으로 업데이트 로직 생성
    patterns.forEach((pattern: any, index: number) => {
      luaContent += `    -- 패턴 ${index + 1}: ${pattern.keyPattern}\n`;
      if (pattern.usesAccountId) {
        luaContent += `    updated_keys = updated_keys + scan_and_update_keys("${pattern.keyPattern.replace('{accountId}', '" .. old_gnid .. "')}", old_gnid, new_gnid)\n`;
      }
      if (pattern.usesPubId) {
        luaContent += `    updated_keys = updated_keys + scan_and_update_keys("${pattern.keyPattern.replace('{pubId}', '" .. old_nid .. "')}", old_nid, new_nid)\n`;
      }
    });

    luaContent += `
    return updated_keys
end

local total_updated = 0

`;

    records.forEach((record, index) => {
      luaContent += `total_updated = total_updated + update_${instanceName}_redis("${record.uwo_Gnid}", "${record.uwo_Nid}", "${record.uwogl_Gnid}", "${record.uwogl_Nid}")\n`;
    });

    luaContent += this.generateLuaFooter();

    await fs.writeFile(filePath, luaContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드, ${patterns.length}개 패턴) - 전역`);
  }

  private async generateUserCacheScript(records: RemapRecord[], outputDir: string): Promise<void> {
    const fileName = 'user_cache_update.lua';
    const filePath = path.join(outputDir, fileName);

    let luaContent = this.generateLuaHeader('User Cache Redis', records.length);
    
    luaContent += `
-- 사용자 캐시 업데이트 함수 (SCAN 기반 최적화)
local function update_user_cache(old_gnid, old_nid, new_gnid, new_nid)
    local updated_keys = 0

    -- SCAN을 사용한 안전한 키 검색 함수
    local function scan_and_update(pattern, old_value, new_value)
        local cursor = "0"
        local count = 0

        repeat
            local result = redis.call('SCAN', cursor, 'MATCH', pattern, 'COUNT', 100)
            cursor = result[1]
            local keys = result[2]

            for _, key in ipairs(keys) do
                -- 키 타입 확인
                local key_type = redis.call('TYPE', key)['ok']

                if key_type == 'string' then
                    local value = redis.call('GET', key)
                    if value then
                        local new_key = string.gsub(key, old_value, new_value)
                        redis.call('SET', new_key, value)
                        redis.call('DEL', key)
                        count = count + 1
                    end
                elseif key_type == 'hash' then
                    local new_key = string.gsub(key, old_value, new_value)
                    redis.call('RENAME', key, new_key)
                    count = count + 1
                elseif key_type == 'list' or key_type == 'set' or key_type == 'zset' then
                    local new_key = string.gsub(key, old_value, new_value)
                    redis.call('RENAME', key, new_key)
                    count = count + 1
                end
            end
        until cursor == "0"

        return count
    end

    -- 사용자 기본 정보 키 패턴들
    local patterns = {
        "user:" .. old_gnid .. "*",
        "user_info:" .. old_gnid .. "*",
        "user_status:" .. old_gnid .. "*",
        "character:" .. old_nid .. "*",
        "char_info:" .. old_nid .. "*",
        "char_status:" .. old_nid .. "*"
    }

    -- GNID 관련 키 업데이트
    for i = 1, 3 do
        updated_keys = updated_keys + scan_and_update(patterns[i], old_gnid, new_gnid)
    end

    -- NID 관련 키 업데이트
    for i = 4, 6 do
        updated_keys = updated_keys + scan_and_update(patterns[i], old_nid, new_nid)
    end

    return updated_keys
end

-- 메인 실행 부분
local total_updated = 0

`;

    // 각 레코드에 대한 업데이트 호출
    records.forEach((record, index) => {
      luaContent += `-- 레코드 ${index + 1}: ${record.uwo_Gnid} -> ${record.uwogl_Gnid}\n`;
      luaContent += `total_updated = total_updated + update_user_cache("${record.uwo_Gnid}", "${record.uwo_Nid}", "${record.uwogl_Gnid}", "${record.uwogl_Nid}")\n\n`;
    });

    luaContent += this.generateLuaFooter();

    await fs.writeFile(filePath, luaContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드)`);
  }

  private async generateUserRedisScript(records: RemapRecord[], outputDir: string): Promise<void> {
    const fileName = 'user_redis_update.lua';
    const filePath = path.join(outputDir, fileName);

    let luaContent = this.generateLuaHeader('User Redis', records.length);
    
    luaContent += `
-- 사용자 Redis 업데이트 함수 (SCAN 기반)
local function update_user_redis(old_gnid, old_nid, new_gnid, new_nid)
    local updated_keys = 0

    -- SCAN 기반 업데이트 함수 (재사용)
    local function scan_and_update_keys(pattern, old_value, new_value)
        local cursor = "0"
        local count = 0

        repeat
            local result = redis.call('SCAN', cursor, 'MATCH', pattern, 'COUNT', 100)
            cursor = result[1]
            local keys = result[2]

            for _, key in ipairs(keys) do
                local key_type = redis.call('TYPE', key)['ok']
                local new_key = string.gsub(key, old_value, new_value)

                if key_type == 'string' then
                    local value = redis.call('GET', key)
                    if value then
                        redis.call('SET', new_key, value)
                        redis.call('DEL', key)
                        count = count + 1
                    end
                else
                    -- 복합 데이터 타입은 RENAME 사용
                    redis.call('RENAME', key, new_key)
                    count = count + 1
                end
            end
        until cursor == "0"

        return count
    end

    -- 사용자 세션 및 상태 키 패턴들
    local gnid_patterns = {
        "session:" .. old_gnid .. "*",
        "login:" .. old_gnid .. "*",
        "online:" .. old_gnid .. "*"
    }

    local nid_patterns = {
        "location:" .. old_nid .. "*",
        "world_pos:" .. old_nid .. "*"
    }

    -- GNID 관련 키 업데이트
    for _, pattern in ipairs(gnid_patterns) do
        updated_keys = updated_keys + scan_and_update_keys(pattern, old_gnid, new_gnid)
    end

    -- NID 관련 키 업데이트
    for _, pattern in ipairs(nid_patterns) do
        updated_keys = updated_keys + scan_and_update_keys(pattern, old_nid, new_nid)
    end

    return updated_keys
end

local total_updated = 0

`;

    records.forEach((record, index) => {
      luaContent += `total_updated = total_updated + update_user_redis("${record.uwo_Gnid}", "${record.uwo_Nid}", "${record.uwogl_Gnid}", "${record.uwogl_Nid}")\n`;
    });

    luaContent += this.generateLuaFooter();

    await fs.writeFile(filePath, luaContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드)`);
  }

  private async generateTownRedisScript(records: RemapRecord[], outputDir: string): Promise<void> {
    const fileName = 'town_redis_update.lua';
    const filePath = path.join(outputDir, fileName);

    let luaContent = this.generateLuaHeader('Town Redis', records.length);
    
    luaContent += `
-- 도시 관련 Redis 업데이트 함수
local function update_town_redis(old_gnid, old_nid, new_gnid, new_nid)
    local updated_keys = 0
    
    -- 도시 관련 키 패턴들
    local key_patterns = {
        "town_investment:" .. old_gnid,
        "town_contribution:" .. old_gnid,
        "port_permit:" .. old_gnid,
        "discovery:" .. old_nid
    }
    
    for _, pattern in ipairs(key_patterns) do
        local keys = redis.call('KEYS', pattern)
        for _, key in ipairs(keys) do
            local value = redis.call('GET', key)
            if value then
                local new_key = string.gsub(key, old_gnid, new_gnid)
                new_key = string.gsub(new_key, old_nid, new_nid)
                
                redis.call('SET', new_key, value)
                redis.call('DEL', key)
                updated_keys = updated_keys + 1
            end
        end
    end
    
    return updated_keys
end

local total_updated = 0

`;

    records.forEach((record, index) => {
      luaContent += `total_updated = total_updated + update_town_redis("${record.uwo_Gnid}", "${record.uwo_Nid}", "${record.uwogl_Gnid}", "${record.uwogl_Nid}")\n`;
    });

    luaContent += this.generateLuaFooter();

    await fs.writeFile(filePath, luaContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드)`);
  }

  private async generateNationRedisScript(records: RemapRecord[], outputDir: string): Promise<void> {
    const fileName = 'nation_redis_update.lua';
    const filePath = path.join(outputDir, fileName);

    let luaContent = this.generateLuaHeader('Nation Redis', records.length);
    
    luaContent += `
-- 국가 관련 Redis 업데이트 함수
local function update_nation_redis(old_gnid, old_nid, new_gnid, new_nid)
    local updated_keys = 0
    
    local key_patterns = {
        "nation_member:" .. old_gnid,
        "nation_contribution:" .. old_gnid,
        "nation_rank:" .. old_gnid
    }
    
    for _, pattern in ipairs(key_patterns) do
        local keys = redis.call('KEYS', pattern)
        for _, key in ipairs(keys) do
            local value = redis.call('GET', key)
            if value then
                local new_key = string.gsub(key, old_gnid, new_gnid)
                new_key = string.gsub(new_key, old_nid, new_nid)
                
                redis.call('SET', new_key, value)
                redis.call('DEL', key)
                updated_keys = updated_keys + 1
            end
        end
    end
    
    return updated_keys
end

local total_updated = 0

`;

    records.forEach((record, index) => {
      luaContent += `total_updated = total_updated + update_nation_redis("${record.uwo_Gnid}", "${record.uwo_Nid}", "${record.uwogl_Gnid}", "${record.uwogl_Nid}")\n`;
    });

    luaContent += this.generateLuaFooter();

    await fs.writeFile(filePath, luaContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드)`);
  }

  private async generateGuildRedisScript(records: RemapRecord[], outputDir: string): Promise<void> {
    const fileName = 'guild_redis_update.lua';
    const filePath = path.join(outputDir, fileName);

    let luaContent = this.generateLuaHeader('Guild Redis', records.length);
    
    luaContent += `
-- 길드 관련 Redis 업데이트 함수
local function update_guild_redis(old_gnid, old_nid, new_gnid, new_nid)
    local updated_keys = 0
    
    local key_patterns = {
        "guild_member:" .. old_gnid,
        "guild_contribution:" .. old_gnid,
        "guild_rank:" .. old_gnid
    }
    
    for _, pattern in ipairs(key_patterns) do
        local keys = redis.call('KEYS', pattern)
        for _, key in ipairs(keys) do
            local value = redis.call('GET', key)
            if value then
                local new_key = string.gsub(key, old_gnid, new_gnid)
                new_key = string.gsub(new_key, old_nid, new_nid)
                
                redis.call('SET', new_key, value)
                redis.call('DEL', key)
                updated_keys = updated_keys + 1
            end
        end
    end
    
    return updated_keys
end

local total_updated = 0

`;

    records.forEach((record, index) => {
      luaContent += `total_updated = total_updated + update_guild_redis("${record.uwo_Gnid}", "${record.uwo_Nid}", "${record.uwogl_Gnid}", "${record.uwogl_Nid}")\n`;
    });

    luaContent += this.generateLuaFooter();

    await fs.writeFile(filePath, luaContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드)`);
  }

  private async generateRankingRedisScript(records: RemapRecord[], outputDir: string): Promise<void> {
    const fileName = 'ranking_redis_update.lua';
    const filePath = path.join(outputDir, fileName);

    let luaContent = this.generateLuaHeader('Ranking Redis', records.length);
    
    luaContent += `
-- 랭킹 관련 Redis 업데이트 함수
local function update_ranking_redis(old_gnid, old_nid, new_gnid, new_nid)
    local updated_keys = 0
    
    local key_patterns = {
        "ranking:" .. old_nid,
        "leaderboard:" .. old_nid,
        "score:" .. old_nid
    }
    
    for _, pattern in ipairs(key_patterns) do
        local keys = redis.call('KEYS', pattern)
        for _, key in ipairs(keys) do
            local value = redis.call('GET', key)
            if value then
                local new_key = string.gsub(key, old_gnid, new_gnid)
                new_key = string.gsub(new_key, old_nid, new_nid)
                
                redis.call('SET', new_key, value)
                redis.call('DEL', key)
                updated_keys = updated_keys + 1
            end
        end
    end
    
    return updated_keys
end

local total_updated = 0

`;

    records.forEach((record, index) => {
      luaContent += `total_updated = total_updated + update_ranking_redis("${record.uwo_Gnid}", "${record.uwo_Nid}", "${record.uwogl_Gnid}", "${record.uwogl_Nid}")\n`;
    });

    luaContent += this.generateLuaFooter();

    await fs.writeFile(filePath, luaContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드)`);
  }

  private generateLuaHeader(redisInstance: string, recordCount: number): string {
    const timestamp = new Date().toISOString();
    return `-- =====================================================
-- UWO GNID/NID 리맵핑 Redis Lua 스크립트
-- Redis 인스턴스: ${redisInstance}
-- 생성일시: ${timestamp}
-- 레코드 수: ${recordCount}
-- =====================================================

`;
  }

  private generateLuaFooter(): string {
    return `
-- 결과 반환
return "업데이트된 키 수: " .. total_updated
`;
  }

  // Redis Shell Script 생성 메서드들
  async generateRedisShellScripts(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    outputDir: string,
    codebaseAnalysisResult: any
  ): Promise<void> {
    console.log('🔴 Redis Shell 스크립트 생성 중...');

    // artifacts/scripts 폴더 생성
    const scriptsDir = path.join(outputDir, 'scripts');
    await fs.ensureDir(scriptsDir);

    // Redis 전용 업데이트 스크립트 생성
    await this.generateRedisUpdateScript(records, scriptsDir, codebaseAnalysisResult);

    console.log('✅ Redis Shell 스크립트 생성 완료');
  }

  private async generateRedisUpdateScript(
    records: RemapRecord[],
    scriptsDir: string,
    codebaseAnalysisResult: any
  ): Promise<void> {
    const fileName = 'update_redis.sh';
    const filePath = path.join(scriptsDir, fileName);

    let script = this.generateRedisShellHeader('Redis 데이터 업데이트', records.length);

    script += `
# Redis 연결 설정
REDIS_HOST="\${REDIS_HOST:-localhost}"
REDIS_PORT="\${REDIS_PORT:-6379}"
REDIS_AUTH="\${REDIS_AUTH:-}"

# 스크립트 디렉토리
SCRIPT_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
SQL_DIR="\$(dirname "\$SCRIPT_DIR")"

echo "🔴 Redis 데이터 업데이트 시작..."
echo "📁 Lua 스크립트 디렉토리: \$SQL_DIR"

# 에러 발생시 중단
set -e

# Redis 연결 테스트
echo "🔍 Redis 연결 테스트 중..."
if [ -n "\$REDIS_AUTH" ]; then
    redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" ping > /dev/null
else
    redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" ping > /dev/null
fi
echo "✅ Redis 연결 성공"

`;

    // 전역 Redis 스크립트 실행
    script += `
echo "🌐 전역 Redis 스크립트 실행 중..."

# Auth Redis 업데이트
if [ -f "\$SQL_DIR/auth_redis_update.lua" ]; then
    echo "🔐 Auth Redis 업데이트 중..."
    if [ -n "\$REDIS_AUTH" ]; then
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" --eval "\$SQL_DIR/auth_redis_update.lua"
    else
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$SQL_DIR/auth_redis_update.lua"
    fi
    echo "✅ Auth Redis 업데이트 완료"
fi

# UserCache Redis 업데이트
if [ -f "\$SQL_DIR/userCache_redis_update.lua" ]; then
    echo "👤 UserCache Redis 업데이트 중..."
    if [ -n "\$REDIS_AUTH" ]; then
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" --eval "\$SQL_DIR/userCache_redis_update.lua"
    else
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$SQL_DIR/userCache_redis_update.lua"
    fi
    echo "✅ UserCache Redis 업데이트 완료"
fi

# Order Redis 업데이트
if [ -f "\$SQL_DIR/order_redis_update.lua" ]; then
    echo "📋 Order Redis 업데이트 중..."
    if [ -n "\$REDIS_AUTH" ]; then
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" --eval "\$SQL_DIR/order_redis_update.lua"
    else
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$SQL_DIR/order_redis_update.lua"
    fi
    echo "✅ Order Redis 업데이트 완료"
fi

`;

    // 월드별 Redis 스크립트 실행
    const worldIds = [...new Set(records.map(r => r.gameServerId))];
    for (const worldId of worldIds) {
      script += `
echo "🌍 ${worldId} 월드 Redis 스크립트 실행 중..."

# User Redis 업데이트
if [ -f "\$SQL_DIR/${worldId}/user_redis_update.lua" ]; then
    echo "👥 ${worldId} User Redis 업데이트 중..."
    if [ -n "\$REDIS_AUTH" ]; then
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" --eval "\$SQL_DIR/${worldId}/user_redis_update.lua"
    else
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$SQL_DIR/${worldId}/user_redis_update.lua"
    fi
    echo "✅ ${worldId} User Redis 업데이트 완료"
fi

# Town Redis 업데이트
if [ -f "\$SQL_DIR/${worldId}/town_redis_update.lua" ]; then
    echo "🏘️ ${worldId} Town Redis 업데이트 중..."
    if [ -n "\$REDIS_AUTH" ]; then
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" --eval "\$SQL_DIR/${worldId}/town_redis_update.lua"
    else
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$SQL_DIR/${worldId}/town_redis_update.lua"
    fi
    echo "✅ ${worldId} Town Redis 업데이트 완료"
fi

# Nation Redis 업데이트
if [ -f "\$SQL_DIR/${worldId}/nation_redis_update.lua" ]; then
    echo "🏛️ ${worldId} Nation Redis 업데이트 중..."
    if [ -n "\$REDIS_AUTH" ]; then
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" --eval "\$SQL_DIR/${worldId}/nation_redis_update.lua"
    else
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$SQL_DIR/${worldId}/nation_redis_update.lua"
    fi
    echo "✅ ${worldId} Nation Redis 업데이트 완료"
fi

# Guild Redis 업데이트
if [ -f "\$SQL_DIR/${worldId}/guild_redis_update.lua" ]; then
    echo "⚔️ ${worldId} Guild Redis 업데이트 중..."
    if [ -n "\$REDIS_AUTH" ]; then
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" --eval "\$SQL_DIR/${worldId}/guild_redis_update.lua"
    else
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$SQL_DIR/${worldId}/guild_redis_update.lua"
    fi
    echo "✅ ${worldId} Guild Redis 업데이트 완료"
fi

# Ranking Redis 업데이트
if [ -f "\$SQL_DIR/${worldId}/ranking_redis_update.lua" ]; then
    echo "🏆 ${worldId} Ranking Redis 업데이트 중..."
    if [ -n "\$REDIS_AUTH" ]; then
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" -a "\$REDIS_AUTH" --eval "\$SQL_DIR/${worldId}/ranking_redis_update.lua"
    else
        redis-cli -h "\$REDIS_HOST" -p "\$REDIS_PORT" --eval "\$SQL_DIR/${worldId}/ranking_redis_update.lua"
    fi
    echo "✅ ${worldId} Ranking Redis 업데이트 완료"
fi

`;
    }

    script += `
echo "🎉 Redis 데이터 업데이트 완료!"
echo "📊 총 ${records.length}개 레코드가 업데이트되었습니다."
`;

    await fs.writeFile(filePath, script, 'utf8');
    await fs.chmod(filePath, 0o755); // 실행 권한 부여
    console.log(`📄 생성됨: ${fileName} (Redis 업데이트 스크립트)`);
  }

  private generateRedisShellHeader(description: string, recordCount: number): string {
    const timestamp = new Date().toISOString();
    return `#!/bin/bash
# =====================================================
# UWO GNID/NID 리맵핑 Redis Shell 스크립트
# 설명: ${description}
# 생성일시: ${timestamp}
# 레코드 수: ${recordCount}
# =====================================================

`;
  }
}
