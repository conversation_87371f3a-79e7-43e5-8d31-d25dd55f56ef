import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import { CliOptions } from '../types';
import { SchemaExtractor, saveSchemaToFile } from '../extractor/schemaExtractor';
import { BackupApplication } from '../app/backupApplication';
import path from 'path';
import fs from 'fs-extra';

export class CliInterface {
  private program: Command;

  constructor() {
    this.program = new Command();
    this.setupCommands();
  }

  private setupCommands(): void {
    this.program
      .name('uwo-remap')
      .description('UWO Database and Redis ID Remapping Tool')
      .version('1.0.0');
    this.program
      .option('--analyze-only', 'Run in analysis mode only', false);
    this.program
      .command('run')
      .description('Execute remapping operation')
      .requiredOption('-f, --csv-file <file>', 'CSV file path')
      .option('-w, --world-id <worldId>', 'Process specific world only')
      .option('-d, --dry-run', 'Perform simulation only without actual execution', false)
      .option('-s, --skip-verification', 'Skip verification step', false)
      .option('-b, --batch-size <size>', 'Batch size', '100')
      .option('--schema-dir <dir>', 'Schema files directory', './artifacts/schemas')
      .option('-c, --config-dir <dir>', 'Configuration file directory', './config')
      .option('-l, --log-level <level>', 'Log level', 'info')
      .option('-o, --output-dir <dir>', 'Output directory', './artifacts/reports')
      .action(async (options) => {
        await this.handleRunCommand(options);
      });

    this.program
      .command('analyze')
      .description('Perform codebase analysis only')
      .option('-s, --schema-dir <dir>', 'Schema files directory', './artifacts/schemas')
      .option('-c, --config-dir <dir>', 'Configuration file directory', './config')
      .option('-l, --log-level <level>', 'Log level', 'info')
      .option('-o, --output-dir <dir>', 'Output directory', './artifacts/reports')
      .action(async (options) => {
        await this.handleAnalyzeCommand(options);
      });

    this.program
      .command('verify')
      .description('Verify existing data')
      .requiredOption('-f, --csv-file <file>', 'CSV file path')
      .option('-w, --world-id <worldId>', 'Verify specific world only')
      .option('-c, --config-dir <dir>', 'Configuration file directory', './config')
      .option('-l, --log-level <level>', 'Log level', 'info')
      .option('-o, --output-dir <dir>', 'Output directory', './artifacts/reports')
      .action(async (options) => {
        await this.handleVerifyCommand(options);
      });

    this.program
      .command('clean')
      .description('Clean output directory')
      .option('-o, --output-dir <dir>', 'Output directory to clean', './artifacts')
      .action(async (options) => {
        await this.handleCleanCommand(options);
      });

    this.program
      .command('summary')
      .description('Generate CSV remap data summary and analysis report')
      .requiredOption('-f, --csv-file <file>', 'CSV file path')
      .option('-o, --output-dir <dir>', 'Output directory', './artifacts/reports')
      .option('-l, --log-level <level>', 'Log level', 'info')
      .action(async (options) => {
        await this.handleSummaryCommand(options);
      });

    this.program
      .command('init')
      .description('Initialize configuration files')
      .option('-c, --config-dir <dir>', 'Configuration file directory', './config')
      .action(async (options) => {
        await this.handleInitCommand(options);
      });

    this.program
      .command('backup')
      .description('Create backup of databases and Redis data')
      .option('-w, --world-id <worldId>', 'Backup specific world only')
      .option('-d, --databases <types>', 'Database types to backup (auth,world,user)', 'auth,world,user')
      .option('--no-redis', 'Exclude Redis data from backup')
      .option('--no-compress', 'Do not compress backup files')
      .option('--description <desc>', 'Backup description')
      .option('-c, --config-dir <dir>', 'Configuration file directory', './config')
      .option('-b, --backup-dir <dir>', 'Backup directory', './backup')
      .action(async (options) => {
        await this.handleBackupCommand(options);
      });

    this.program
      .command('restore')
      .description('Restore data from backup')
      .requiredOption('-t, --timestamp <timestamp>', 'Backup timestamp to restore')
      .option('-w, --world-id <worldId>', 'Restore specific world only')
      .option('-d, --databases <types>', 'Database types to restore (auth,world,user)')
      .option('--no-redis', 'Exclude Redis data from restore')
      .option('-f, --force', 'Force restore without confirmation')
      .option('-c, --config-dir <dir>', 'Configuration file directory', './config')
      .option('-b, --backup-dir <dir>', 'Backup directory', './backup')
      .action(async (options) => {
        await this.handleRestoreCommand(options);
      });

    this.program
      .command('list-backups')
      .description('List available backups')
      .option('-b, --backup-dir <dir>', 'Backup directory', './backup')
      .action(async (options) => {
        await this.handleListBackupsCommand(options);
      });

    this.program
      .command('status')
      .description('Check connection status')
      .option('-c, --config-dir <dir>', 'Configuration file directory', './config')
      .action(async (options) => {
        await this.handleStatusCommand(options);
      });

    this.program
      .command('extract-schema')
      .description('Extract table schemas from actual databases')
      .option('-o, --output-dir <dir>', 'Output directory for schema files', './artifacts/schemas')
      .option('--test-only', 'Test database connections only')
      .action(async (options) => {
        await this.handleExtractSchemaCommand(options);
      });

    this.program
      .command('generate')
      .description('SQL 및 Lua 스크립트 파일 생성 (실제 DB 업데이트 없음)')
      .requiredOption('-f, --csv-file <file>', 'CSV 파일 경로')
      .option('-w, --world-id <worldId>', '특정 월드 ID (지정하지 않으면 모든 월드)')
      .option('--dry-run', '드라이런 모드 (스크립트 생성만, 실행 안함)', false)
      .option('--skip-verification', '검증 단계 건너뛰기', false)
      .option('-b, --batch-size <size>', '배치 크기', '1000')
      .option('-c, --config-dir <dir>', '설정 디렉토리', './config')
      .option('-l, --log-level <level>', '로그 레벨', 'info')
      .option('-s, --schema-dir <dir>', '스키마 디렉토리', './artifacts/schemas')
      .option('-o, --output-dir <dir>', '스크립트 출력 디렉토리', './artifacts/scripts')
      .action(async (options) => {
        await this.handleGenerateCommand(options);
      });
  }

  async run(args: string[]): Promise<void> {
    try {
      await this.program.parseAsync(args);
    } catch (error) {
      console.error(chalk.red('Error occurred:'), error);
      process.exit(1);
    }
  }

  private async handleRunCommand(options: any): Promise<void> {
    const cliOptions: CliOptions = {
      csvFile: options.csvFile,
      worldId: options.worldId,
      dryRun: options.dryRun,
      skipVerification: options.skipVerification,
      batchSize: parseInt(options.batchSize),
      configDir: options.configDir,
      logLevel: options.logLevel,
      outputDir: options.outputDir,
      schemaDir: options.schemaDir,
    };

    console.log(chalk.blue.bold('🚀 UWO GNID/NID 리맵핑 도구 시작'));
    console.log(chalk.gray('설정:'));
    console.log(chalk.gray(`  - CSV 파일: ${cliOptions.csvFile}`));
    console.log(chalk.gray(`  - 월드 ID: ${cliOptions.worldId || '전체'}`));
    console.log(chalk.gray(`  - 드라이런: ${cliOptions.dryRun ? '예' : '아니오'}`));
    console.log(chalk.gray(`  - 배치 크기: ${cliOptions.batchSize}`));

    // 1단계: CSV 요약 분석 자동 실행
    console.log(chalk.cyan('\n📊 1단계: CSV 데이터 요약 분석'));
    try {
      await this.performCSVSummary(cliOptions.csvFile, cliOptions.outputDir);
    } catch (error) {
      console.error(chalk.red('❌ CSV 요약 분석 실패:'), error);
      console.log(chalk.yellow('⚠️ 요약 분석에 실패했지만 리맵핑을 계속 진행합니다.'));
    }

    // 2단계: 리맵핑 실행
    console.log(chalk.cyan('\n🔄 2단계: 리맵핑 실행'));

    if (!cliOptions.dryRun) {
      const confirmed = await this.confirmExecution();
      if (!confirmed) {
        console.log(chalk.yellow('Operation cancelled.'));
        return;
      }
    }

    const { RemapApplication } = await import('../app/remapApplication');
    const app = new RemapApplication(cliOptions);
    await app.run();
  }

  private async handleAnalyzeCommand(options: any): Promise<void> {
    console.log(chalk.blue.bold('🔍 Codebase Analysis Started'));

    const { CodebaseAnalyzer } = await import('../analyzer/codebaseAnalyzer');
    const analyzer = new CodebaseAnalyzer();

    const result = await analyzer.analyzeCodebase(options.schemaDir);

    // 결과 저장
    const fs = await import('fs-extra');
    const path = await import('path');

    await fs.ensureDir(options.outputDir);

    // JSON 결과 저장
    const jsonFilePath = path.join(options.outputDir, 'analysis-result.json');
    await fs.writeJson(jsonFilePath, result, { spaces: 2 });
    console.log(chalk.gray(`Analysis result saved: ${jsonFilePath}`));

    // HTML 보고서 생성
    const { HtmlReportGenerator } = await import('../reports/htmlReportGenerator');
    const htmlFilePath = path.join(options.outputDir, 'analysis-report.html');
    await HtmlReportGenerator.generateAnalysisReport(result, htmlFilePath);
    console.log(chalk.gray(`Analysis report generated: ${htmlFilePath}`));

    console.log(chalk.green('✅ Analysis completed'));
    console.log(chalk.gray(`📊 Found ${result.databaseTables.length} Database tables`));
    console.log(chalk.gray(`📊 Found ${result.redisKeys.length} Redis key patterns`));
    console.log(chalk.gray(`⏱️ Analysis time: ${result.analysisTime}ms`));
  }

  private async handleVerifyCommand(options: any): Promise<void> {
    const cliOptions: CliOptions = {
      csvFile: options.csvFile,
      worldId: options.worldId,
      dryRun: true,
      skipVerification: false,
      batchSize: 100,
      configDir: options.configDir,
      logLevel: options.logLevel,
      outputDir: options.outputDir,
    };

    console.log(chalk.blue.bold('✅ Data Verification Started'));

    const { RemapApplication } = await import('../app/remapApplication');
    const app = new RemapApplication(cliOptions);
    await app.verifyOnly();
  }

  private async handleCleanCommand(options: any): Promise<void> {
    console.log(chalk.blue.bold('🧹 Cleaning Output Directory'));

    const fs = await import('fs-extra');
    const path = await import('path');

    try {
      const outputDir = path.resolve(options.outputDir);

      // 디렉토리 존재 확인
      if (await fs.pathExists(outputDir)) {
        // 디렉토리 내용 삭제
        await fs.emptyDir(outputDir);
        console.log(chalk.green(`✅ Cleaned directory: ${outputDir}`));
      } else {
        console.log(chalk.yellow(`⚠️ Directory does not exist: ${outputDir}`));
      }
    } catch (error) {
      console.error(chalk.red('❌ Failed to clean output directory:'), error);
      throw error;
    }
  }

  private async handleInitCommand(options: any): Promise<void> {
    console.log(chalk.blue.bold('⚙️ Configuration File Initialization'));

    const { ConfigLoader } = await import('../config/configLoader');
    const configLoader = new ConfigLoader(options.configDir);

    await configLoader.copyExampleConfigs();

    console.log(chalk.green('✅ Configuration files have been initialized.'));
    console.log(chalk.yellow('📝 Please edit config/database.json to set connection information.'));
  }

  private async handleStatusCommand(options: any): Promise<void> {
    console.log(chalk.blue.bold('📊 Connection Status Check'));

    try {
      const { ConfigLoader } = await import('../config/configLoader');
      const { DatabaseManager } = await import('../database/databaseManager');
      const { RedisManager } = await import('../redis/redisManager');

      const configLoader = new ConfigLoader(options.configDir);

      const configExists = await configLoader.checkConfigFiles();
      console.log(chalk.gray('Configuration Files:'));
      console.log(`  database.json: ${configExists ? chalk.green('✅') : chalk.red('❌')}`);

      if (!configExists) {
        console.log(chalk.yellow('⚠️ Configuration files not found. Initialize with "uwo-remap init" command.'));
        return;
      }

      const remapConfig = await configLoader.loadRemapToolConfig();

      const spinner = this.createSpinner('Checking database connections...');
      const newDbManager = new DatabaseManager(remapConfig);
      await newDbManager.initialize();
      const dbStatus = await newDbManager.checkConnections();
      spinner.stop();

      console.log(chalk.gray('\nDatabase Connections:'));
      console.log(`  - Auth: ${dbStatus.auth ? chalk.green('✅') : chalk.red('❌')}`);

      for (const [worldId, status] of Object.entries(dbStatus.worlds)) {
        console.log(`  - World-${worldId}: ${status ? chalk.green('✅') : chalk.red('❌')}`);
      }

      for (const [shardKey, status] of Object.entries(dbStatus.userShards)) {
        console.log(`  - User-Shard-${shardKey}: ${status ? chalk.green('✅') : chalk.red('❌')}`);
      }

      const redisSpinner = this.createSpinner('Checking Redis connections...');
      const newRedisManager = new RedisManager(remapConfig);
      await newRedisManager.initialize();
      const redisStatus = await newRedisManager.checkConnections();
      redisSpinner.stop();

      console.log(chalk.gray('\nRedis Connections:'));
      for (const [instanceName, status] of Object.entries(redisStatus)) {
        console.log(`  ${instanceName}: ${status ? chalk.green('✅') : chalk.red('❌')}`);
      }

      await newDbManager.cleanup();
      await newRedisManager.cleanup();
    } catch (error) {
      console.error(chalk.red('❌ Status check failed:'), error);
    }
  }

  private async handleExtractSchemaCommand(options: any): Promise<void> {
    console.log(chalk.blue.bold('🔍 Database Schema Extraction Started'));

    try {
      const { ConfigLoader } = await import('../config/configLoader');
      const { DatabaseManager } = await import('../database/databaseManager');

      const configLoader = new ConfigLoader(options.configDir || './config');
      const remapConfig = await configLoader.loadRemapToolConfig();
      const outputDir = path.resolve(options.outputDir);

      // 연결 테스트
      if (options.testOnly) {
        console.log(chalk.gray('🔗 Testing database connections...'));
        const dbManager = new DatabaseManager(remapConfig);
        await dbManager.initialize();
        const dbStatus = await dbManager.checkConnections();

        console.log(`  Auth DB: ${dbStatus.auth ? chalk.green('✅') : chalk.red('❌')}`);

        for (const [worldId, status] of Object.entries(dbStatus.worlds)) {
          console.log(`  World DB (${worldId}): ${status ? chalk.green('✅') : chalk.red('❌')}`);
          break; // 첫 번째만 표시
        }

        for (const [shardKey, status] of Object.entries(dbStatus.userShards)) {
          console.log(`  User DB (${shardKey}): ${status ? chalk.green('✅') : chalk.red('❌')}`);
          break; // 첫 번째만 표시
        }

        await dbManager.cleanup();
        console.log(chalk.green('✅ Connection test completed'));
        return;
      }

      // 스키마 추출
      const firstWorld = remapConfig.worlds[0];
      const firstUserShard = firstWorld?.mysqlUserDb.shards[0];
      const userDbName = firstUserShard?.sqlCfg.database || firstWorld?.mysqlUserDb.sqlDefaultCfg.database;

      const extractors = [
        { type: 'auth' as const, name: 'auth', dbName: remapConfig.sharedConfig.mysqlAuthDb.database },
        { type: 'world' as const, name: 'world', dbName: firstWorld?.mysqlWorldDb.database },
        { type: 'user' as const, name: 'user', dbName: userDbName }
      ].filter(item => item.dbName); // dbName이 있는 것만 필터링

      for (const { type, name, dbName } of extractors) {
        if (!dbName) continue;

        try {
          const spinner = this.createSpinner(`Extracting ${name} schema...`);

          const extractor = new SchemaExtractor(remapConfig);
          await extractor.connect(type, type === 'user' ? '0' : undefined);

          const schema = await extractor.extractSchema(type, dbName);
          await saveSchemaToFile(schema, outputDir);

          await extractor.disconnect();
          spinner.stop();

          console.log(`  ${name}: ${chalk.green('✅')} (${schema.tables.length} tables)`);
        } catch (error) {
          console.error(`  ${name}: ${chalk.red('❌')} ${error}`);
        }
      }

      console.log(chalk.green('✅ Schema extraction completed'));
      console.log(chalk.gray(`📁 Schema files saved to: ${outputDir}`));
      console.log(chalk.yellow('ℹ️ World and User schemas extracted from first instance only (all instances have identical schemas)'));
    } catch (error) {
      console.error(chalk.red('❌ Schema extraction failed:'), error);
      process.exit(1);
    }
  }

  private async confirmExecution(): Promise<boolean> {
    const answer = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirmed',
        message: chalk.yellow('⚠️ Actual database and Redis data will be modified. Do you want to continue?'),
        default: false,
      },
    ]);

    return answer.confirmed;
  }

  createSpinner(text: string): ora.Ora {
    return ora(text).start();
  }

  showSuccess(message: string): void {
    console.log(chalk.green('✅'), message);
  }

  showWarning(message: string): void {
    console.log(chalk.yellow('⚠️'), message);
  }

  showError(message: string): void {
    console.log(chalk.red('❌'), message);
  }

  showInfo(message: string): void {
    console.log(chalk.blue('ℹ️'), message);
  }

  showTable(headers: string[], rows: string[][]): void {
    const columnWidths = headers.map((header, index) => {
      const maxRowWidth = Math.max(...rows.map(row => (row[index] || '').length));
      return Math.max(header.length, maxRowWidth);
    });

    const headerRow = headers.map((header, index) =>
      header.padEnd(columnWidths[index]!!)
    ).join(' | ');
    console.log(chalk.bold(headerRow));

    const separator = columnWidths.map(width => '-'.repeat(width)).join('-+-');
    console.log(separator);

    for (const row of rows) {
      const dataRow = row.map((cell, index) =>
        (cell || '').padEnd(columnWidths[index]!!)
      ).join(' | ');
      console.log(dataRow);
    }
  }

  showProgress(current: number, total: number, message: string): void {
    const percentage = Math.round((current / total) * 100);
    const progressBar = '█'.repeat(Math.floor(percentage / 5)) + '░'.repeat(20 - Math.floor(percentage / 5));
    console.log(`[${progressBar}] ${percentage}% ${message} (${current}/${total})`);
  }

  private async handleBackupCommand(options: any): Promise<void> {
    try {
      const backupApp = new BackupApplication(options.configDir);

      const backupOptions = {
        worldId: options.worldId,
        databases: options.databases,
        includeRedis: !options.noRedis,
        compress: !options.noCompress,
        description: options.description,
        configDir: options.configDir,
        backupDir: options.backupDir
      };

      const result = await backupApp.createBackup(backupOptions);

      if (!result.success) {
        process.exit(1);
      }
    } catch (error) {
      console.error(chalk.red('❌ 백업 실패:'), error);
      process.exit(1);
    }
  }

  private async handleRestoreCommand(options: any): Promise<void> {
    try {
      const backupApp = new BackupApplication(options.configDir);

      const restoreOptions = {
        timestamp: options.timestamp,
        worldId: options.worldId,
        databases: options.databases,
        includeRedis: !options.noRedis,
        force: options.force,
        configDir: options.configDir,
        backupDir: options.backupDir
      };

      const result = await backupApp.restoreBackup(restoreOptions);

      if (!result.success) {
        process.exit(1);
      }
    } catch (error) {
      console.error(chalk.red('❌ 복원 실패:'), error);
      process.exit(1);
    }
  }

  private async handleListBackupsCommand(options: any): Promise<void> {
    try {
      const backupApp = new BackupApplication('./config'); // 기본 설정 디렉토리 사용

      const listOptions = {
        backupDir: options.backupDir
      };

      await backupApp.listBackups(listOptions);
    } catch (error) {
      console.error(chalk.red('❌ 백업 목록 조회 실패:'), error);
      process.exit(1);
    }
  }

  private async handleSummaryCommand(options: any): Promise<void> {
    console.log(chalk.blue.bold('📊 CSV 리맵 데이터 요약 시작'));

    try {
      const { RemapDataSummaryAnalyzer } = await import('../analyzer/remapDataSummaryAnalyzer');

      const analyzer = new RemapDataSummaryAnalyzer();
      const analysisResult = await analyzer.analyzeCSV(options.csvFile);

      // JSON 결과 저장
      const outputDir = path.resolve(options.outputDir);
      await fs.ensureDir(outputDir);

      const jsonPath = path.join(outputDir, 'summary-result.json');
      await fs.writeFile(jsonPath, JSON.stringify(analysisResult, null, 2));

      // HTML 보고서 생성
      const { SummaryReportGenerator } = await import('../reports/summaryReportGenerator');
      const reportGenerator = new SummaryReportGenerator();
      const htmlPath = path.join(outputDir, 'summary-report.html');
      await reportGenerator.generateReport(analysisResult, htmlPath);

      console.log(chalk.green('✅ 요약 완료'));
      console.log(chalk.gray(`📄 JSON 결과: ${jsonPath}`));
      console.log(chalk.gray(`📊 HTML 보고서: ${htmlPath}`));

      // 간단한 요약 출력
      console.log(chalk.cyan('\n📈 요약:'));
      console.log(`  총 레코드 수: ${analysisResult.totalRecords.toLocaleString()}`);
      console.log(`  고유 계정 수 (GNID): ${analysisResult.uniqueAccounts.toLocaleString()}`);
      console.log(`  고유 캐릭터 수 (NID): ${analysisResult.uniqueUsers.toLocaleString()}`);
      console.log(`  월드 수: ${analysisResult.worldDistribution.length}`);
      console.log(`  전체 중복 수: ${analysisResult.duplicates.reduce((sum, dup) => sum + dup.duplicates.length, 0)}`);
      console.log(`  검증 문제 수: ${analysisResult.validationIssues.length}`);

    } catch (error) {
      console.error(chalk.red('❌ 요약 실패:'), error);
      process.exit(1);
    }
  }

  private async handleGenerateCommand(options: any): Promise<void> {
    const cliOptions: CliOptions = {
      csvFile: options.csvFile,
      worldId: options.worldId,
      dryRun: options.dryRun,
      skipVerification: options.skipVerification,
      batchSize: parseInt(options.batchSize),
      configDir: options.configDir,
      logLevel: options.logLevel,
      outputDir: options.outputDir,
      schemaDir: options.schemaDir,
    };

    console.log(chalk.blue.bold('🔧 UWO GNID/NID 스크립트 생성 도구 시작'));
    console.log(chalk.gray('설정:'));
    console.log(chalk.gray(`  - CSV 파일: ${cliOptions.csvFile}`));
    console.log(chalk.gray(`  - 월드 ID: ${cliOptions.worldId || '전체'}`));
    console.log(chalk.gray(`  - 스크립트 출력 디렉토리: ${cliOptions.outputDir}`));
    console.log(chalk.gray(`  - 배치 크기: ${cliOptions.batchSize}`));

    // 1단계: CSV 요약 분석 자동 실행
    console.log(chalk.cyan('\n📊 1단계: CSV 데이터 요약 분석'));
    try {
      await this.performCSVSummary(cliOptions.csvFile, './output');
    } catch (error) {
      console.error(chalk.red('❌ CSV 요약 분석 실패:'), error);
      console.log(chalk.yellow('⚠️ 요약 분석에 실패했지만 스크립트 생성을 계속 진행합니다.'));
    }

    // 2단계: 스크립트 생성
    console.log(chalk.cyan('\n🔧 2단계: SQL 및 Lua 스크립트 생성'));

    try {
      const { ScriptGenerator } = await import('../generators/scriptGenerator');
      const generator = new ScriptGenerator();

      await generator.generateScripts(cliOptions);

      console.log(chalk.green('\n✅ 스크립트 생성 완료!'));
      console.log(chalk.gray(`📁 생성된 스크립트 위치: ${cliOptions.outputDir}`));

    } catch (error) {
      console.error(chalk.red('❌ 스크립트 생성 실패:'), error);
      process.exit(1);
    }
  }

  /**
   * CSV 요약 분석 수행
   */
  private async performCSVSummary(csvFile: string, outputDir: string): Promise<void> {
    const { RemapDataSummaryAnalyzer } = await import('../analyzer/remapDataSummaryAnalyzer');

    const analyzer = new RemapDataSummaryAnalyzer();
    const analysisResult = await analyzer.analyzeCSV(csvFile);

    // JSON 결과 저장
    await fs.ensureDir(outputDir);
    const jsonPath = path.join(outputDir, 'summary-result.json');
    await fs.writeFile(jsonPath, JSON.stringify(analysisResult, null, 2));

    // HTML 보고서 생성
    const { SummaryReportGenerator } = await import('../reports/summaryReportGenerator');
    const reportGenerator = new SummaryReportGenerator();
    const htmlPath = path.join(outputDir, 'summary-report.html');
    await reportGenerator.generateReport(analysisResult, htmlPath);

    console.log(chalk.green('✅ CSV 요약 분석 완료'));
    console.log(chalk.gray(`📄 JSON 결과: ${jsonPath}`));
    console.log(chalk.gray(`📊 HTML 보고서: ${htmlPath}`));

    // 간단한 요약 출력
    console.log(chalk.cyan('📈 요약:'));
    console.log(`  총 레코드 수: ${analysisResult.totalRecords.toLocaleString()}`);
    console.log(`  고유 계정 수 (GNID): ${analysisResult.uniqueAccounts.toLocaleString()}`);
    console.log(`  고유 캐릭터 수 (NID): ${analysisResult.uniqueUsers.toLocaleString()}`);
    console.log(`  월드 수: ${analysisResult.worldDistribution.length}`);
    console.log(`  전체 중복 수: ${analysisResult.duplicates.reduce((sum, dup) => sum + dup.duplicates.length, 0)}`);
    console.log(`  검증 문제 수: ${analysisResult.validationIssues.length}`);

    // 문제가 있으면 경고 표시
    if (analysisResult.duplicates.length > 0) {
      console.log(chalk.yellow(`⚠️ ${analysisResult.duplicates.length}개 카테고리에서 중복 발견`));
    }
    if (analysisResult.validationIssues.length > 0) {
      console.log(chalk.yellow(`⚠️ ${analysisResult.validationIssues.length}개 검증 문제 발견`));
    }

    // 문제가 있으면 사용자에게 확인 요청
    if (analysisResult.duplicates.length > 0 || analysisResult.validationIssues.length > 0) {
      console.log(chalk.yellow('\n⚠️ CSV 데이터에 문제가 발견되었습니다.'));
      console.log(chalk.yellow('상세한 내용은 HTML 보고서를 확인하세요.'));
      console.log(chalk.yellow('계속 진행하시겠습니까? (Ctrl+C로 중단 가능)'));

      // 3초 대기
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
}
